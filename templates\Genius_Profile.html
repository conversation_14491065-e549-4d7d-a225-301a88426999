<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="GigGenius - Professional freelancer profile and portfolio">
    <title>GigGenius - Profile</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --text-light: #ffffff;
            --text-dark: #333333;
            --gray-100: #f8f9fa;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-400: #ced4da;
            --gray-500: #adb5bd;
            --gray-600: #6c757d;
            --gray-700: #495057;
            --gray-800: #343a40;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --border-radius-sm: 4px;
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --border-radius-xl: 20px;
            --transition-fast: 0.2s ease;
            --transition: 0.3s ease;
        }

        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: #f8f9fa;
            color: var(--text-dark);
            line-height: 1.6;
            min-height: 100vh;
        }

        a {
            text-decoration: none;
            color: inherit;
        }

        ul {
            list-style: none;
        }

        button, input, select, textarea {
            font-family: inherit;
            font-size: inherit;
            outline: none;
        }

        button {
            cursor: pointer;
            border: none;
            background: none;
        }

        img, video {
            max-width: 100%;
            height: auto;
        }

        /* Layout */
        .header-container {
            max-width: 2000px;
            margin: 0 auto;
            background-color: white;
            box-shadow: var(--shadow-sm);
        }

        .body-container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(30px);
            box-shadow:
                0 30px 80px rgba(0, 0, 0, 0.15),
                0 15px 40px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            border-radius: 32px 32px 0 0;
            border: 1px solid rgba(255, 255, 255, 0.6);
            min-height: calc(100vh - 5rem);
            display: flex;
            flex-direction: column;
            margin-top: 3rem;
            overflow: hidden;
            position: relative;
        }

        .main-content {
            padding: 2rem 1.5rem 2.5rem;
            flex: 1;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
            position: relative;
        }

        .main-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 10% 20%, rgba(0, 74, 173, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 90% 80%, rgba(205, 32, 139, 0.03) 0%, transparent 50%);
            pointer-events: none;
        }

        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 1rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 5rem;
            position: relative;
            z-index: 1000;
            min-height: 5rem;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 2rem;
            padding-left: 0;
            flex: 1;
        }

        .logo {
            display: flex;
            align-items: center;
            color: var(--primary-pink);
        }

        .logo img {
            width: 3.5rem;
            height: 3.5rem;
        }

        .logo h1 {
            font-size: 1.5rem;
            font-weight: bold;
            margin-left: 0.5rem;
            margin-right: 0.5rem;
            color: var(--primary-pink);
        }

        .logo:hover, .logo:active {
            color: var(--primary-blue);
        }

        .logo:hover h1 {
            color: var(--primary-blue);
        }

        .nav-links {
            display: flex;
            gap: 1rem;
            align-items: center;
            height: 100%;
            margin: 0;
        }

        .nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            padding: 0.5rem 1rem;
            font-size: 1rem;
            font-weight: 500;
            border-radius: 6px;
            transition: all 0.3s ease;
            position: relative;
        }
        /* Remove underline from individual nav items */
        .nav-links > a:after,
        .nav-dropbtn:after {
            display: none !important;
        }
        .nav-links > a,
        .nav-links > a:hover,
        .nav-links > a.active,
        .nav-dropbtn,
        .nav-dropbtn:hover,
        .nav-dropbtn.active,
        .nav-dropdown-content a,
        .nav-dropdown-content a:hover {
            text-decoration: none !important;
            border-bottom: none !important;
        }
        .nav-links > a:hover, .nav-links > a.active,
        .nav-dropbtn:hover, .nav-dropbtn.active {
            color: var(--primary-pink);
        }
        .nav-dropdown-content a:after {
            display: none !important;
        }
        .navbar {
            position: relative;
        }
        .nav-links > a:hover, .nav-links > a.active {
            color: var(--primary-pink);
            background-color: transparent;
        }
        .nav-dropbtn:hover, .nav-dropbtn.active {
            color: var(--primary-pink);
            background-color: transparent;
        }

        .nav-dropbtn {
            font-weight: 600;
            font-size: 1rem;
            color: var(--primary-blue);
            background: none;
            border: none;
            padding: 0.5rem 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border-radius: 6px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-dropdown {
            position: relative;
            display: inline-block;
        }

        .nav-dropbtn {
            font-weight: 600;
            font-size: 1rem;
            color: var(--primary-blue);
            background: none;
            border: none;
            padding: 0.5rem 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border-radius: 6px;
            transition: all 0.3s ease;
            border-bottom: 2px solid transparent;
            position: relative;
        }

        .nav-dropbtn:hover, .nav-dropbtn.active {
            color: var(--primary-pink);
            background-color: transparent;
            border-bottom: 2px solid var(--primary-pink);
        }

        .nav-dropdown-content {
            display: none;
            position: absolute;
            background-color: #fff;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            top: 100%;
            left: 0;
        }

        .nav-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            font-size: 1rem;
        }

        .nav-dropdown-content a:hover {
            background-color: transparent;
            color: var(--primary-pink);
        }

        .nav-dropdown:hover .nav-dropdown-content {
            display: block;
        }

        /* Right section container */
        .right-section {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            padding-right: 1rem;
            justify-content: flex-end;
        }

        /* Search and Auth Styles */
        .search-container {
            display: flex;
            align-items: center;
            margin-right: 1rem;
        }

        .search-bar {
            position: relative;
            display: flex;
            align-items: center;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 25px;
            padding: 0.4rem 1rem;
            width: 200px;
            transition: all 0.3s ease;
        }

        .search-bar:focus-within {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px rgba(0, 74, 173, 0.1);
        }

        .search-bar input {
            border: none;
            background: transparent;
            outline: none;
            flex: 1;
            font-size: 0.9rem;
            color: #333;
        }

        .search-bar input::placeholder {
            color: #6c757d;
        }

        .search-bar .icon {
            color: var(--primary-blue);
            cursor: pointer;
            margin-left: 0.5rem;
        }

        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* Notification Styles */
        .notification-container {
            position: relative;
        }

        .notification-icon {
            position: relative;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: background-color 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: transparent;
        }

        .notification-icon:hover {
            background-color: transparent;
        }

        .notification-icon i {
            font-size: 1.2rem;
            color: var(--primary-blue);
        }

        .notification-badge {
            position: absolute;
            top: 0;
            right: 0;
            background: var(--primary-pink);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid white;
        }

        .notification-dropdown {
            position: absolute;
            top: calc(100% + 10px);
            right: 0;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            width: 380px;
            max-height: 500px;
            overflow: hidden;
            z-index: 1000;
            border: 1px solid rgba(0, 74, 173, 0.08);
            display: none;
        }

        .notification-dropdown.active {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .notification-header {
            padding: 16px 20px 12px;
            border-bottom: 1px solid rgba(0, 74, 173, 0.06);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 700;
            color: var(--primary-blue);
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            position: relative;
        }

        .notification-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            border-radius: 16px 16px 0 0;
        }

        .notification-header-actions {
            font-size: 0.8rem;
            color: var(--primary-pink);
            cursor: pointer;
            padding: 6px 12px;
            border-radius: 8px;
            transition: all 0.2s ease;
            font-weight: 600;
            background: rgba(205, 32, 139, 0.05);
            border: 1px solid rgba(205, 32, 139, 0.1);
        }

        .notification-header-actions:hover {
            background: rgba(205, 32, 139, 0.1);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(205, 32, 139, 0.15);
        }

        .empty-notifications {
            padding: 40px 20px;
            text-align: center;
            color: #6b7280;
            background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);
        }

        .empty-notifications i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.3;
        }

        .empty-notifications h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .empty-notifications p {
            margin: 0;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* Profile Dropdown Styles */
        .profile-dropdown {
            position: relative;
        }

        .profile-button {
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            border: 2px solid var(--primary-blue);
            transition: all 0.3s ease;
        }

        .profile-button:hover {
            border-color: var(--primary-pink);
            transform: scale(1.05);
        }

        .profile-button img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-dropdown-content {
            position: absolute;
            top: calc(100% + 10px);
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            min-width: 200px;
            z-index: 10000;
            border: 1px solid rgba(0, 74, 173, 0.08);
            overflow: hidden;
            display: none;
        }

        .profile-dropdown-content.active {
            display: block;
            animation: slideDown 0.3s ease;
        }

        .profile-dropdown-content a {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.2s ease;
            border-bottom: 1px solid rgba(0, 74, 173, 0.05);
        }

        .profile-dropdown-content a:last-child {
            border-bottom: none;
        }

        .profile-dropdown-content a:hover {
            background: rgba(0, 74, 173, 0.05);
            color: var(--primary-pink);
            transform: translateX(4px);
        }

        .profile-dropdown-content a i {
            width: 16px;
            text-align: center;
            opacity: 0.7;
        }

        .dropdown-divider {
            height: 1px;
            background-color: #eee;
            margin: 8px 0;
        }

        .logout-option {
            color: #dc3545 !important;
        }

        .logout-option:hover {
            background-color: #fff5f5 !important;
            color: #dc3545 !important;
        }

        /* Show dropdown on click */
        .profile-dropdown.active .profile-dropdown-content {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Mobile Responsive Styles */
        @media (max-width: 992px) {
            .navbar-left {
                padding-left: 0;
            }

            .logo img {
                width: 2.4rem;
                height: 2.4rem;
                border-radius: 50%;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                flex-shrink: 0;
            }

            .logo h1 {
                font-size: 1.1rem;
                margin: 0;
                white-space: nowrap;
                font-weight: 600;
                color: var(--primary-pink);
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .nav-links {
                gap: 0.5rem;
            }

            .nav-links a {
                font-size: 0.9rem;
                padding: 0.4rem 0.8rem;
            }

            .nav-dropbtn {
                font-size: 0.9rem;
                padding: 0.4rem 0.8rem;
            }

            .search-bar {
                width: 160px;
                height: 36px;
            }
        }

        @media (max-width: 768px) {
            .navbar {
                height: 4rem;
                padding: 0 0.8rem;
            }

            .logo h1 {
                font-size: 1.2rem;
            }

            .nav-links {
                gap: 0.3rem;
            }

            .nav-links a {
                font-size: 0.8rem;
                padding: 0.3rem 0.6rem;
            }

            .nav-dropbtn {
                font-size: 0.8rem;
                padding: 0.3rem 0.6rem;
            }

            .search-bar {
                width: 140px;
                height: 34px;
            }

            .right-section {
                gap: 0.8rem;
            }

            .body-container {
                margin-top: 1rem;
                border-radius: 16px 16px 0 0;
            }

            .main-content {
                padding: 1.5rem 1rem 2rem;
            }

            .profile-name {
                font-size: 2rem;
            }

            .profile-title {
                font-size: 1.1rem;
            }

            .profile-stats {
                grid-template-columns: 1fr;
                gap: 0.8rem;
            }

            .stat-item {
                padding: 1.2rem 0.8rem;
            }

            .section-header {
                padding: 1.5rem 1.5rem 1rem;
            }

            .section-content {
                padding: 1.2rem;
            }

            .section-title {
                font-size: 1.25rem;
            }
        }

        /* Simplified animations removed */

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Enhanced focus states */
        *:focus {
            outline: 2px solid var(--primary-blue);
            outline-offset: 2px;
        }

        /* Loading animation for images */
        .portfolio-image {
            position: relative;
            overflow: hidden;
        }

        .portfolio-image::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: shimmer 2s infinite;
        }





        /* Profile Section */
        .profile-section {
            margin-bottom: 2rem;
            position: relative;
            padding: 1rem 0;
        }

        .profile-section::before {
            content: '';
            position: absolute;
            top: -0.5rem;
            left: -1rem;
            right: -1rem;
            height: 150px;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            opacity: 0.03;
            border-radius: 16px;
            z-index: -1;
        }

        .profile-header {
            margin-bottom: 1.5rem;
            text-align: center;
            padding: 1rem 0;
        }

        .profile-name {
            font-size: 2.8rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 70%, #f093fb 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.8rem;
            letter-spacing: -0.02em;
            line-height: 1.1;
            position: relative;
        }

        .profile-title {
            font-size: 1.4rem;
            color: #4a5568;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.8rem;
            font-weight: 600;
            margin-bottom: 1rem;
            line-height: 1.3;
        }

        .profile-title i {
            color: var(--primary-pink);
            font-size: 1.2rem;
            filter: drop-shadow(0 2px 4px rgba(205, 32, 139, 0.3));
        }



        .profile-row {
            display: flex;
            flex-direction: column;
            gap: 0;
            margin-bottom: 1.5rem;
            align-items: stretch;
        }

        @media (min-width: 1024px) {
            .profile-row {
                flex-direction: row;
                gap: 0;
                align-items: stretch;
            }
        }

        .video-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(10px);
            border-radius: 16px 0 0 16px;
            border: 1px solid rgba(0, 74, 173, 0.1);
            border-right: none;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.04);
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            padding: 1rem;
            display: flex;
            align-items: center;
            margin-bottom: 0;
        }

        .profile-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            border-radius: 0 16px 16px 0;
            border: 1px solid rgba(0, 74, 173, 0.1);
            border-left: none;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.04);
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            padding: 1.8rem;
            margin-bottom: 0;
        }

        .video-card::before, .profile-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .video-card:hover, .profile-card:hover {
            transform: translateY(-6px) scale(1.01);
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(0, 74, 173, 0.1);
        }

        .video-container:hover {
            border-color: rgba(0, 74, 173, 0.25);
            box-shadow:
                0 8px 30px rgba(0, 0, 0, 0.12),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
        }

        .video-card:hover::before, .profile-card:hover::before {
            opacity: 1;
        }

        @media (min-width: 1024px) {
            .video-card {
                width: 45%;
                border-radius: 16px 0 0 16px;
                border-right: none;
            }

            .profile-card {
                width: 55%;
                border-radius: 0 16px 16px 0;
                border-left: none;
            }
        }

        @media (max-width: 1023px) {
            .video-card {
                border-radius: 16px;
                border: 1px solid rgba(0, 74, 173, 0.1);
                margin-bottom: 0.5rem;
            }

            .profile-card {
                border-radius: 16px;
                border: 1px solid rgba(0, 74, 173, 0.1);
            }
        }

        /* Enhanced Video Container - Professional styling */
        .video-container {
            position: relative;
            width: 100%;
            height: auto;
            min-height: 300px;
            overflow: hidden;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid rgba(0, 74, 173, 0.1);
            border-radius: 16px;
            transition: all 0.3s ease;
            margin: 0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.06);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .video-container:hover {
            border-color: #004AAD;
            box-shadow: 0 6px 20px rgba(0, 74, 173, 0.15);
            transform: translateY(-3px);
        }

        .video-container video {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 16px;
            min-height: 480px;
        }

        .expand-btn {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            z-index: 2;
            transition: background-color 0.3s;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.875rem;
        }

        .expand-btn:hover {
            background: rgba(0, 0, 0, 0.7);
        }

        .video-container.expanded {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            padding: 0;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .video-container.expanded video {
            width: 90%;
            height: 90%;
            object-fit: contain;
        }

        /* Profile Content */
        .profile-content {
            padding: 1rem 1.8rem 1.8rem 1.8rem;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        .profile-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5rem;
            margin-bottom: 1rem;
            padding: 0;
        }

        @media (min-width: 768px) {
            .profile-stats {
                grid-template-columns: repeat(4, 1fr);
                gap: 0.5rem;
            }
        }

        .stat-item {
            text-align: center;
            padding: 1.5rem 1rem;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.9) 100%);
            border-radius: 16px;
            border: 1px solid rgba(0, 74, 173, 0.1);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.06);
            position: relative;
            overflow: hidden;
        }

        .stat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
        }

        .stat-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 70%, #f093fb 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            display: block;
            line-height: 1.1;
            position: relative;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #4a5568;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            line-height: 1.2;
        }

        .profile-summary {
            margin-bottom: 1rem;
            margin-top: 0;
        }

        .profile-summary h3 {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .profile-summary h3 i {
            color: var(--primary-blue);
        }

        .summary-text {
            color: var(--gray-600);
            margin-bottom: 0.5rem;
        }

        .show-more {
            color: var(--primary-pink);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
        }

        .show-more:hover {
            text-decoration: underline;
        }

        .edit-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            color: white;
            font-size: 0.9rem;
            font-weight: 700;
            cursor: pointer;
            margin-left: 0.75rem;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            border: none;
            border-radius: 16px;
            padding: 0.75rem 1.5rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            box-shadow:
                0 8px 25px rgba(0, 74, 173, 0.3),
                0 4px 12px rgba(205, 32, 139, 0.2);
            position: relative;
            overflow: hidden;
        }

        .edit-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .edit-btn:hover {
            transform: translateY(-4px) scale(1.05);
            box-shadow:
                0 15px 40px rgba(0, 74, 173, 0.4),
                0 8px 20px rgba(205, 32, 139, 0.3);
        }

        .edit-btn:hover::before {
            left: 100%;
        }

        .profile-fields {
            margin-bottom: 1.5rem;
        }

        .field-group {
            margin-bottom: 1rem;
        }

        .field-group label {
            display: block;
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 0.3rem;
            color: #2d3748;
        }

        .field-input {
            width: 100%;
            padding: 0.6rem;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 0.85rem;
            background-color: #f7fafc;
            color: #2d3748;
        }

        .edit-profile-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem 2rem;
            background: linear-gradient(135deg, var(--primary-pink) 0%, #e91e63 100%);
            color: white;
            border: none;
            border-radius: 16px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            width: 100%;
            justify-content: center;
            box-shadow: 0 8px 25px rgba(212, 27, 140, 0.3);
            position: relative;
            overflow: hidden;
        }

        .edit-profile-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .edit-profile-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(212, 27, 140, 0.4);
        }

        .edit-profile-btn:hover::before {
            left: 100%;
        }

        /* Portfolio and Work History Section */
        .portfolio-section {
            display: flex;
            flex-direction: column;
            gap: 1.2rem;
            margin-top: 1rem;
        }

        .section-row {
            display: flex;
            flex-direction: column;
            gap: 0;
            margin-bottom: 1rem;
        }

        @media (min-width: 1024px) {
            .section-row {
                flex-direction: row;
                gap: 0;
            }
        }

        .section-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            border: 1px solid rgba(0, 74, 173, 0.1);
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.04);
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            padding: 1.8rem;
            margin-bottom: 0;
        }

        .section-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .section-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .section-card:hover::before {
            opacity: 1;
        }

        @media (min-width: 1024px) {
            .section-card:first-child {
                width: 50%;
                border-radius: 16px 0 0 16px;
                border-right: none;
            }

            .section-card:last-child {
                width: 50%;
                border-radius: 0 16px 16px 0;
                border-left: none;
            }
        }

        @media (max-width: 1023px) {
            .section-card {
                border-radius: 16px;
                border: 1px solid rgba(0, 74, 173, 0.1);
                margin-bottom: 0.5rem;
            }

            .section-card:last-child {
                margin-bottom: 0;
            }
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 2.5rem 3rem 2rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.08);
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.8) 100%);
            backdrop-filter: blur(10px);
            position: relative;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: var(--text-dark);
            letter-spacing: -0.01em;
        }

        .section-title i {
            color: var(--primary-blue);
            font-size: 1.25rem;
            padding: 0.5rem;
            background: rgba(0, 74, 173, 0.1);
            border-radius: 12px;
        }

        .section-content {
            padding: 2rem;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
            position: relative;
        }

        .introduction-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.8rem;
        }

        .textarea {
            width: 100%;
            min-height: 120px;
            padding: 0.8rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 0.85rem;
            resize: vertical;
            margin-bottom: 1rem;
            background-color: #f7fafc;
            color: #2d3748;
            line-height: 1.5;
            background-color: #f9fafb;
        }

        .section-actions {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .arrow-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background-color: #f3f4f6;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s;
        }

        .arrow-btn:hover {
            background-color: #e5e7eb;
            color: #333;
        }

        .add-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: linear-gradient(135deg, var(--primary-pink) 0%, #e91e63 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 6px 20px rgba(212, 27, 140, 0.25);
            position: relative;
            overflow: hidden;
        }

        .add-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .add-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(212, 27, 140, 0.35);
        }

        .add-btn:hover::before {
            left: 100%;
        }

        .portfolio-image {
            width: 100%;
            height: 250px;
            border-radius: 6px;
            overflow: hidden;
            margin-bottom: 1.25rem;
            background-color: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
        }

        .portfolio-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }

        .portfolio-image:hover img {
            transform: scale(1.05);
        }

        /* Enhanced Portfolio Design */
        .portfolio-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, #f8f9ff 0%, #fff5f8 100%);
            border-radius: 16px;
            border: 1px solid rgba(0, 74, 173, 0.1);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .portfolio-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #333;
            margin: 0;
            font-family: 'Poppins', sans-serif;
        }

        .portfolio-actions {
            display: flex;
            gap: 0.75rem;
        }

        .portfolio-add-btn, .portfolio-refresh-btn {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(135deg, #004AAD 0%, #0066cc 100%);
            color: white;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 74, 173, 0.3);
            position: relative;
            overflow: hidden;
        }

        .portfolio-add-btn:hover, .portfolio-refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 74, 173, 0.4);
            background: linear-gradient(135deg, #003a8c 0%, #0052a3 100%);
        }

        .portfolio-add-btn:active, .portfolio-refresh-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 10px rgba(0, 74, 173, 0.3);
        }

        .portfolio-tabs {
            display: flex;
            gap: 0;
            margin-bottom: 2rem;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 0.5rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .portfolio-tab {
            padding: 0.875rem 2rem;
            background: none;
            border: none;
            font-size: 1rem;
            font-weight: 600;
            color: #6c757d;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-family: 'Poppins', sans-serif;
            position: relative;
            flex: 1;
            text-align: center;
        }

        .portfolio-tab.active {
            background: linear-gradient(135deg, #004AAD 0%, #0066cc 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(0, 74, 173, 0.3);
            transform: translateY(-1px);
        }

        .portfolio-tab:hover:not(.active) {
            background: rgba(0, 74, 173, 0.1);
            color: #004AAD;
        }

        /* Enhanced empty state styling */
        .portfolio-empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: linear-gradient(135deg, #f8f9ff 0%, #fff5f8 100%);
            border-radius: 20px;
            border: 2px dashed rgba(0, 74, 173, 0.2);
            margin: 2rem 0;
            position: relative;
            overflow: hidden;
        }

        .portfolio-empty-state::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(0, 74, 173, 0.05) 0%, transparent 70%);
            animation: pulse 4s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.5; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.1); }
        }

        .portfolio-empty-icon {
            font-size: 4rem;
            background: linear-gradient(135deg, #004AAD 0%, #CD208B 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 1;
        }

        .portfolio-empty-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.75rem;
            font-family: 'Poppins', sans-serif;
            position: relative;
            z-index: 1;
        }

        .portfolio-empty-subtitle {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 2rem;
            line-height: 1.6;
            position: relative;
            z-index: 1;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }

        .portfolio-empty-cta {
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            background: linear-gradient(135deg, #004AAD 0%, #0066cc 100%);
            color: white;
            padding: 1rem 2rem;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            font-family: 'Poppins', sans-serif;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 74, 173, 0.3);
            position: relative;
            z-index: 1;
            cursor: pointer;
            border: none;
            font-size: 1rem;
        }

        .portfolio-empty-cta:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 74, 173, 0.4);
            text-decoration: none;
            color: white;
        }

        /* Ensure portfolio content visibility */
        .portfolio-content {
            width: 100%;
            min-height: 200px;
        }

        .portfolio-content[style*="display: none"] {
            display: none !important;
        }

        .portfolio-content[style*="display: block"] {
            display: block !important;
        }

        .portfolio-tab:hover {
            color: var(--text-dark);
        }

        .portfolio-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            padding: 0 1.25rem;
            margin-bottom: 2rem;
        }

        .portfolio-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Portfolio cards will be controlled by JavaScript navigation */
        .portfolio-card {
            display: none; /* Hide all portfolio cards by default */
        }

        /* Show only the active portfolio card */
        .portfolio-card.active {
            display: block;
        }

        .portfolio-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .portfolio-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            pointer-events: none;
        }

        /* Portfolio Card Header */
        .portfolio-card-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .portfolio-card-title {
            margin: 0 0 0.5rem 0;
            font-size: 1.4rem;
            font-weight: 700;
            color: #2d3748;
            line-height: 1.3;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .portfolio-card-subtitle {
            margin: 0 0 1rem 0;
            font-size: 0.9rem;
            font-weight: 500;
            color: #667eea;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Portfolio Card Content */
        .portfolio-card-content {
            background: rgba(255, 255, 255, 0.98);
            padding: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .portfolio-card-description {
            color: #4a5568;
            font-size: 0.95rem;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        /* Skills Section */
        .portfolio-skills {
            margin-bottom: 1.5rem;
        }

        .portfolio-skills-label {
            font-size: 0.85rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .portfolio-skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .portfolio-skill-tag {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Enhanced View Project Button */
        .portfolio-card .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 0.8rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .portfolio-card .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }

        .portfolio-card .btn:active {
            transform: translateY(0);
        }

        .portfolio-card .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .portfolio-card .btn:hover::before {
            left: 100%;
        }

        /* Portfolio View Modal Styles - Matching Edit Profile Design */
        .portfolio-view-modal {
            display: none;
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(20px);
        }

        .portfolio-view-modal-content {
            background: white;
            margin: 3% auto;
            padding: 0;
            border-radius: 28px;
            width: 90%;
            max-width: 900px;
            max-height: 85vh;
            overflow-y: auto;
            box-shadow: 0 40px 120px rgba(0,0,0,0.2), 0 20px 60px rgba(0,0,0,0.1);
            position: relative;
            border: 1px solid rgba(255,255,255,0.3);
            animation: modalSlideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            flex-direction: column;
        }

        .portfolio-modal-header {
            background: linear-gradient(135deg, #004AAD 0%, #CD208B 100%);
            padding: 2rem;
            border-radius: 28px 28px 0 0;
            position: relative;
            color: white;
        }

        .portfolio-modal-close {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            position: absolute;
            top: 2rem;
            right: 2rem;
        }

        .portfolio-modal-close:hover {
            background: rgba(255,255,255,0.3);
        }

        .portfolio-modal-title {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 700;
            font-family: 'Poppins', sans-serif;
            color: white;
            line-height: 1.2;
        }

        .portfolio-modal-subtitle {
            margin: 0;
            opacity: 0.9;
            font-size: 0.9rem;
            color: white;
            font-weight: 400;
        }

        .portfolio-modal-body {
            padding: 2.5rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);
            position: relative;
            flex: 1;
            overflow-y: auto;
            min-height: 0;
        }

        .portfolio-modal-section {
            margin-bottom: 2.5rem;
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255,255,255,0.8);
            position: relative;
            z-index: 1;
        }

        .portfolio-modal-section:last-child {
            margin-bottom: 0;
        }

        .portfolio-modal-section-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #004AAD;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
            font-family: 'Poppins', sans-serif;
        }

        .portfolio-modal-section-title::before {
            content: '';
            width: 6px;
            height: 6px;
            background: #004AAD;
            border-radius: 50%;
        }

        .portfolio-modal-description {
            color: #495057;
            line-height: 1.7;
            font-size: 1rem;
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid #e9ecef;
            margin: 0;
        }

        .portfolio-modal-skills {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
        }

        .portfolio-modal-skill-tag {
            background: linear-gradient(135deg, #004AAD, #CD208B);
            color: white;
            padding: 0.5rem 1.25rem;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 500;
            border: none;
            box-shadow: 0 4px 15px rgba(0, 74, 173, 0.3);
            transition: all 0.3s ease;
            font-family: 'Poppins', sans-serif;
        }

        .portfolio-modal-skill-tag:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(205, 32, 139, 0.4);
        }

        .portfolio-modal-url {
            color: #004AAD;
            text-decoration: none;
            word-break: break-all;
            background: #f8fafc;
            padding: 1.25rem;
            border-radius: 15px;
            border: 1px solid #e2e8f0;
            display: block;
            font-weight: 500;
            transition: all 0.3s ease;
            font-family: 'Poppins', sans-serif;
        }

        .portfolio-modal-url:hover {
            background: #e3f2fd;
            border-color: #004AAD;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0, 74, 173, 0.2);
        }

        .portfolio-modal-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            padding: 1.5rem 2.5rem;
            border-top: 1px solid #e2e8f0;
            background: white;
            border-radius: 0 0 28px 28px;
            flex-shrink: 0;
            position: sticky;
            bottom: 0;
            z-index: 10;
        }

        .portfolio-modal-btn {
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.95rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            min-width: 120px;
            justify-content: center;
            font-family: 'Poppins', sans-serif;
        }

        .portfolio-modal-btn-primary {
            background: linear-gradient(135deg, #004AAD, #CD208B);
            color: white;
            border: none;
            box-shadow: 0 4px 15px rgba(0, 74, 173, 0.3);
        }

        .portfolio-modal-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(205, 32, 139, 0.4);
        }

        .portfolio-modal-btn-secondary {
            background: white;
            color: #004AAD;
            border: 2px solid #004AAD;
        }

        .portfolio-modal-btn-secondary:hover {
            background: #f8fafc;
            transform: translateY(-2px);
            border-color: #CD208B;
            color: #CD208B;
        }

        /* Portfolio Navigation Arrows */
        .portfolio-modal-navigation {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, #004AAD, #CD208B);
            border: none;
            color: white;
            font-size: 1.5rem;
            width: 50px;
            height: 50px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(0, 74, 173, 0.4);
            z-index: 1001;
        }

        .portfolio-modal-navigation:hover {
            transform: translateY(-50%) scale(1.15);
            box-shadow: 0 8px 25px rgba(205, 32, 139, 0.6);
        }

        .portfolio-modal-navigation.prev {
            left: -25px;
        }

        .portfolio-modal-navigation.next {
            right: -25px;
        }

        .portfolio-modal-navigation:disabled {
            opacity: 0.3;
            cursor: not-allowed;
            transform: translateY(-50%);
        }

        .portfolio-modal-navigation:disabled:hover {
            transform: translateY(-50%);
            box-shadow: 0 6px 20px rgba(0, 74, 173, 0.4);
        }

        .portfolio-pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.75rem;
            padding: 2rem 1.25rem;
            margin-top: 2rem;
        }

        .pagination-btn {
            width: 44px;
            height: 44px;
            border-radius: 12px;
            border: 2px solid #e5e7eb;
            background: white;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            font-family: 'Poppins', sans-serif;
            font-size: 0.95rem;
        }

        .pagination-btn:hover {
            border-color: #004AAD;
            color: #004AAD;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 74, 173, 0.2);
        }

        .pagination-btn.active {
            background: linear-gradient(135deg, #004AAD 0%, #0066cc 100%);
            color: white;
            border-color: #004AAD;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 74, 173, 0.4);
        }

        .pagination-btn.prev,
        .pagination-btn.next {
            border-radius: 50%;
            background: #f8f9fa;
        }

        .pagination-btn.prev:hover,
        .pagination-btn.next:hover {
            background: #004AAD;
            color: white;
        }

        .pagination-dots {
            color: #6c757d;
            margin: 0 0.5rem;
            font-weight: 600;
        }

        .input-field {
            width: 100%;
            padding: 0.625rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            margin-bottom: 0.75rem;
        }

        .action-buttons {
            display: flex;
            gap: 0.75rem;
            margin-top: 1rem;
        }

        .action-edit-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.5rem 0.75rem;
            background-color: var(--primary-blue);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .action-edit-btn:hover {
            background-color: #1d4ed8;
        }

        .delete-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.5rem 0.75rem;
            background-color: var(--primary-pink);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .delete-btn:hover {
            background-color: #b91c77;
        }

        .work-history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.25rem;
        }

        .work-history-title {
            display: inline-block;
            padding: 0.5rem 0.75rem;
            background-color: var(--primary-pink);
            color: white;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .certification-card {
            background-color: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            margin-bottom: 1.25rem;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .certification-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .certification-header {
            padding: 1.25rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .certification-title {
            font-size: 1.125rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .certification-title i {
            color: var(--primary-pink);
        }

        .certification-content {
            padding: 1.25rem;
        }

        .certification-detail {
            margin-bottom: 0.75rem;
        }

        .certification-detail strong {
            font-weight: 600;
        }

        .certification-description {
            margin-top: 1rem;
        }

        .certification-description h4 {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .certification-description h4 i {
            color: var(--primary-blue);
        }

        .certification-description p {
            color: var(--gray-600);
        }

        /* Footer */
        footer {
            background: var(--primary-blue);
            padding: 2rem 5%;
            align-items: center;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            gap: 2rem;
            margin-bottom: 2rem;
        }

        @media (min-width: 768px) {
            .footer-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 1024px) {
            .footer-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        .footer-column h3 {
            margin-bottom: 1rem;
            color: var(--text-light);
            font-weight: 600;
        }

        .footer-column a {
            display: block;
            color: var(--text-light);
            text-decoration: none;
            margin-bottom: 0.5rem;
            transition: text-decoration 0.3s ease;
            opacity: 0.8;
        }

        .footer-column a:hover {
            text-decoration: underline;
            opacity: 1;
        }

        .footer-bottom {
            color: var(--text-light);
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        @media (max-width: 768px) {
            .footer-bottom {
                flex-direction: column;
                text-align: center;
            }
        }

        .footer-bottom p {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 400;
        }

        .social-icons {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .social-icons a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            margin: 0;
        }

        .social-icons .bi {
            font-size: 1.2rem;
            color: var(--text-light);
            transition: transform 0.3s ease, color 0.3s ease;
        }

        .social-icons a:hover {
            background-color: var(--primary-pink);
            transform: translateY(-3px);
        }

        .footer-bottom a {
            color: var(--text-light);
            margin: 0 10px;
            text-decoration: none;
        }

        .footer-bottom a:hover {
            text-decoration: underline;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.5) 100%);
            z-index: 10500;
            justify-content: center;
            align-items: center;
            backdrop-filter: blur(20px);
            animation: modalFadeIn 0.4s ease-out;
        }

        .modal.active {
            display: flex !important;
        }

        /* Delete Confirmation Modal */
        .delete-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            z-index: 2000;
            justify-content: center;
            align-items: center;
        }

        .delete-modal.active {
            display: flex;
        }

        .delete-modal-content {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            max-width: 400px;
            width: 90%;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .delete-modal-header {
            margin-bottom: 1.5rem;
        }

        .delete-modal-header h3 {
            color: #dc2626;
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .delete-modal-body {
            margin-bottom: 2rem;
        }

        .delete-modal-body p {
            color: #6b7280;
            font-size: 1rem;
            margin: 0;
            line-height: 1.5;
        }

        .delete-modal-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .delete-modal-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 100px;
        }

        .delete-modal-btn.cancel {
            background: #f3f4f6;
            color: #374151;
        }

        .delete-modal-btn.cancel:hover {
            background: #e5e7eb;
        }

        .delete-modal-btn.confirm {
            background: #dc2626;
            color: white;
        }

        .delete-modal-btn.confirm:hover {
            background: #b91c1c;
        }

        /* Side toast notifications will use dynamic styles */

        /* Video Action Button Styles */
        .video-action-btn {
            font-family: 'Poppins', sans-serif;
            position: relative;
            overflow: hidden;
        }

        .video-action-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed !important;
            transform: none !important;
        }

        .video-action-btn:disabled:hover {
            background: inherit !important;
            transform: none !important;
            box-shadow: inherit !important;
        }

        .video-action-btn:active {
            transform: translateY(1px) !important;
        }

        .video-action-btn .fas {
            transition: transform 0.2s ease;
        }

        .video-action-btn:hover .fas {
            transform: scale(1.1);
        }

        /* Loading state for save button */
        .video-action-btn.loading {
            pointer-events: none;
        }

        .video-action-btn.loading .fas {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes modalFadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Responsive video container adjustments */
        @media (max-width: 768px) {
            .video-container {
                height: 350px;
            }

            .video-container video {
                min-height: 350px;
            }
        }

        @media (min-width: 769px) and (max-width: 1199px) {
            .video-container {
                height: 420px;
            }

            .video-container video {
                min-height: 420px;
            }
        }

        @media (min-width: 1200px) {
            .video-container {
                height: 520px;
            }

            .video-container video {
                min-height: 520px;
            }
        }

        @media (min-width: 1400px) {
            .video-container {
                height: 650px;
            }

            .video-container video {
                height: 100% !important;
                object-fit: cover !important;
            }
        }

        .modal-content {
            background-color: white;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            overflow-y: auto;
        }

        .modal-header {
            border-bottom: 1px solid #e5e7eb;
        }

        .modal-header-content {
            display: flex;
            align-items: center;
            height: 80px;
            padding: 0 2rem;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #000;
            margin-bottom: 20px;
            text-align: center;
        }

        .modal-textarea {
            width: 100%;
            height: 300px;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 20px;
            outline: none;
            resize: none;
            font-size: 16px;
            font-family: inherit;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .modal-textarea:focus {
            border-color: #8b5cf6;
            box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
        }

        .modal-buttons {
            margin-top: 20px;
            text-align: center;
        }

        .modal-back-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            background: transparent;
            color: #000;
            text-decoration: underline;
        }

        .modal-back-btn:hover {
            color: #8b5cf6;
        }

        .modal-next-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            background-color: var(--primary-pink);
            color: #fff;
            margin-left: 10px;
        }

        .modal-next-btn:hover {
            background-color: #b91c77;
        }



        /* Portfolio Content Styling */
        .portfolio-card .content-block img {
            max-width: 100% !important;
            height: auto !important;
            border-radius: 4px;
            margin: 0.5rem 0;
        }

        .portfolio-card .content-block video {
            max-width: 100% !important;
            height: auto !important;
            border-radius: 4px;
            margin: 0.5rem 0;
        }

        .portfolio-card .content-block {
            margin-bottom: 0.5rem !important;
            padding: 0.5rem !important;
            border: 1px solid #eee !important;
            border-radius: 4px !important;
        }

        /* Hide any unwanted text content in project content area */
        .project-content p:empty,
        .project-content div:empty,
        .project-content span:empty {
            display: none !important;
        }

        /* Hide text nodes that might contain file paths or HTML code */
        .project-content {
            overflow: hidden;
        }

        /* Hide all text content in project content area - show only images and videos */
        .project-content p,
        .project-content div:not(.content-block),
        .project-content span,
        .project-content h1,
        .project-content h2,
        .project-content h3,
        .project-content h4,
        .project-content h5,
        .project-content h6,
        .project-content b,
        .project-content strong,
        .project-content em,
        .project-content i:not(.fas):not(.far):not(.fab),
        .project-content small,
        .project-content code,
        .project-content pre {
            display: none !important;
        }

        /* Hide all text nodes and HTML entities */
        .project-content {
            font-size: 0 !important;
            line-height: 0 !important;
        }

        /* Show only image and video content blocks */
        .project-content .content-block {
            display: block !important;
            font-size: 0 !important;
        }

        /* Hide text blocks, PDF blocks, and link blocks */
        .project-content .text-block,
        .project-content .pdf-block,
        .project-content .link-block {
            display: none !important;
        }

        /* Hide any text within content blocks except for images and videos */
        .project-content .content-block p,
        .project-content .content-block span,
        .project-content .content-block h1,
        .project-content .content-block h2,
        .project-content .content-block h3,
        .project-content .content-block h4,
        .project-content .content-block h5,
        .project-content .content-block h6,
        .project-content .content-block b,
        .project-content .content-block strong,
        .project-content .content-block em,
        .project-content .content-block i:not(.fas):not(.far):not(.fab),
        .project-content .content-block small,
        .project-content .content-block code,
        .project-content .content-block pre,
        .project-content .content-block div:not(:has(img)):not(:has(video)):not(:has(audio)) {
            display: none !important;
        }

        /* Ensure images and videos are always visible */
        .project-content .content-block img,
        .project-content .content-block video,
        .project-content .content-block audio {
            display: block !important;
            font-size: initial !important;
        }

        /* Hide any stray text nodes that might contain HTML entities */
        .project-content::before,
        .project-content::after {
            content: none !important;
        }

        /* Remove any text content from content blocks */
        .project-content .content-block {
            color: transparent !important;
            text-indent: -9999px !important;
        }

        /* But keep images visible */
        .project-content .content-block img,
        .project-content .content-block video,
        .project-content .content-block audio {
            color: initial !important;
            text-indent: 0 !important;
        }

        .project-content > *:not(img):not(video):not(audio):not(.content-block) {
            display: none !important;
        }

        /* Enhanced Edit Profile Modal */
        @keyframes modalSlideUp {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Responsive Design for Edit Profile Modal */
        @media (max-width: 768px) {
            #editProfileModal .modal-content {
                max-width: 95% !important;
                margin: 1% auto !important;
            }

            #editProfileModal .modal-body > div:first-child {
                display: block !important;
                gap: 2rem !important;
            }

            #editProfileModal .modal-body > div:first-child > div:first-child {
                margin-bottom: 2rem;
            }

            #editProfileModal .modal-body > div:first-child > div:last-child {
                padding: 1.5rem !important;
            }

            #editProfileModal header {
                padding: 1.5rem !important;
            }

            #editProfileModal .modal-body {
                padding: 1.5rem !important;
            }

            /* Make two-column layouts single column on mobile */
            #editProfileModal div[style*="grid-template-columns: 1fr 1fr"] {
                grid-template-columns: 1fr !important;
            }
        }

        @media (max-width: 480px) {
            #editProfileModal .modal-content {
                max-width: 98% !important;
                margin: 0.5% auto !important;
                border-radius: 12px !important;
            }

            #editProfileModal header {
                padding: 1rem !important;
            }

            #editProfileModal .modal-body {
                padding: 1rem !important;
            }

            #editProfileModal .modal-body > div:first-child > div:last-child {
                padding: 1rem !important;
            }
        }

        .profile-photo-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.75rem;
        }

        .profile-photo {
            width: 150px;
            height: 150px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            border-radius: 8px;
        }

        .profile-photo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .upload-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.375rem;
            padding: 0.5rem 0.75rem;
            background-color: #f3f4f6;
            color: #333;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .upload-btn:hover {
            background-color: #e5e7eb;
        }

        .upload-note {
            font-size: 0.75rem;
            color: #6b7280;
        }

        .profile-details-section {
            flex: 1;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-group label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: var(--primary-blue);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-control {
            width: 100%;
            padding: 1rem 1.25rem;
            border: 2px solid rgba(0, 74, 173, 0.1);
            border-radius: 12px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            color: #333;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 4px rgba(0, 74, 173, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
            background: rgba(255, 255, 255, 0.95);
        }

        .form-control[readonly] {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            cursor: not-allowed;
            border-color: rgba(0, 0, 0, 0.1);
        }

        .form-row {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        @media (min-width: 640px) {
            .form-row {
                flex-direction: row;
            }
        }

        .form-col {
            flex: 1;
        }

        .next-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background-color: var(--primary-pink);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
            width: 100%;
            margin-top: 1rem;
        }

        .next-btn:hover {
            background-color: #b91c77;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                padding: 0 1rem;
            }

            .main-content {
                padding: 1rem;
            }

            .profile-name {
                font-size: 1.5rem;
            }

            .profile-title {
                font-size: 1rem;
            }

            .modal-content {
                width: 95%;
                margin: 1rem;
            }

            .modal-body {
                padding: 1rem;
            }

            .profile-form {
                flex-direction: column;
            }
        }

        /* Premium Text Styling */
        .field-label {
            font-weight: 600;
            color: var(--gray-700);
            font-size: 0.95rem;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .field-value {
            color: var(--gray-800);
            font-size: 1.1rem;
            line-height: 1.6;
            font-weight: 500;
        }

        /* Simplified animations */

        /* Utility Classes */
        .hidden {
            display: none;
        }

        .flex {
            display: flex;
        }

        .items-center {
            align-items: center;
        }

        .justify-between {
            justify-content: space-between;
        }

        .gap-2 {
            gap: 0.5rem;
        }

        .gap-4 {
            gap: 1rem;
        }

        .mb-4 {
            margin-bottom: 1rem;
        }

        .mb-6 {
            margin-bottom: 1.5rem;
        }

        .text-center {
            text-align: center;
        }

        .font-bold {
            font-weight: bold;
        }

        .font-semibold {
            font-weight: 600;
        }

        .text-sm {
            font-size: 0.875rem;
        }

        .text-lg {
            font-size: 1.125rem;
        }

        .text-xl {
            font-size: 1.25rem;
        }

        .text-2xl {
            font-size: 1.5rem;
        }

        .text-3xl {
            font-size: 1.875rem;
        }

        .w-full {
            width: 100%;
        }

        .h-full {
            height: 100%;
        }

        .rounded {
            border-radius: 0.25rem;
        }

        .rounded-lg {
            border-radius: 0.5rem;
        }

        .shadow-sm {
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }

        .shadow-md {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .transition-all {
            transition: all 0.3s ease;
        }

        .hover\:shadow-md:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .hover\:transform:hover {
            transform: translateY(-2px);
        }

        /* Star rating */
        .star-rating {
            color: #FBBF24;
        }

        /* Toggle switch */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #22C55E;
            transition: .4s;
            border-radius: 34px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
            transform: translateX(20px);
        }

        /* Work History Styles */
        .work-history-summary {
            background-color: #f9fafb;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }

        .work-history-summary h3 {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .work-history-summary p {
            color: #374151;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }

        .work-history-summary .show-more-btn {
            color: var(--primary-blue);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            background: none;
            border: none;
            padding: 0;
        }

        .work-history-summary .ai-note {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 0.75rem;
        }

        .skills-section {
            margin-bottom: 1.5rem;
        }

        .skills-section h3 {
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.75rem;
        }

        .skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .skill-tag {
            background-color: #f3f4f6;
            color: #374151;
            font-size: 0.75rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
        }

        .job-tabs {
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 1.5rem;
        }

        .job-tabs .tab-list {
            display: flex;
            gap: 1.5rem;
        }

        .job-tab {
            background: none;
            border: none;
            padding: 0.5rem 0;
            font-size: 1rem;
            font-weight: 500;
            color: #6b7280;
            cursor: pointer;
            position: relative;
            transition: color 0.2s;
        }

        .job-tab.active {
            color: #000;
        }

        .job-tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background: #000;
        }

        .job-tab:hover {
            color: #000;
        }

        .job-list {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .job-item {
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 1.5rem;
        }

        .job-item:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }

        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.25rem;
        }

        .job-title {
            color: var(--primary-blue);
            font-weight: 500;
            margin: 0;
        }

        .job-menu-btn {
            background: none;
            border: none;
            color: #6b7280;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .job-menu-btn:hover {
            background-color: #f3f4f6;
        }

        .job-rating {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.25rem;
        }

        .job-rating .stars {
            display: flex;
            color: #FBBF24;
        }

        .job-rating .rating-score {
            font-weight: 500;
        }

        .job-rating .separator {
            color: #6b7280;
        }

        .job-date {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 0.75rem;
        }

        .job-feedback {
            font-size: 0.875rem;
            color: #374151;
            margin-bottom: 0.75rem;
        }

        .job-earnings {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .job-total {
            font-weight: 500;
        }

        .job-rate, .job-hours {
            font-size: 0.875rem;
            color: #6b7280;
        }



        /* Simplified notification styles */

        /* Video Container Auto-Height Override - Perfect Fit */
        .video-container {
            height: auto !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            padding: 0 !important;
        }

        .video-container video {
            position: static !important;
            transform: none !important;
            top: auto !important;
            left: auto !important;
            width: 100% !important;
            height: auto !important;
            max-width: 100% !important;
            object-fit: contain !important;
            display: block !important;
            min-height: auto !important;
        }

        /* ULTIMATE BLACK BACKGROUND FIX - FORCE FILL ENTIRE CONTAINER */
        #videoContainer {
            background: #000 !important;
            border-radius: 16px !important;
            overflow: hidden !important;
            position: relative !important;
            height: 480px !important;
            width: 100% !important;
            padding: 0 !important;
            margin: 0 !important;
            box-sizing: border-box !important;
        }

        #profileVideo,
        #previewVideo {
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            object-fit: fill !important;
            border-radius: 16px !important;
            background: #000 !important;
            transform: none !important;
            display: block !important;
            min-width: 100% !important;
            min-height: 100% !important;
            max-width: 100% !important;
            max-height: 100% !important;
        }

        /* Force video containers to be black */
        .video-container,
        .video-player,
        .video-preview {
            background: #000 !important;
        }

    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar">
        <div class="navbar-left">
                <a href="{{ url_for('landing_page') }}" style="text-decoration: none;">
                    <div class="logo">
                        <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                        <h1>GigGenius</h1>
                    </div>
                </a>

                <div class="nav-links" id="navLinks">
                    <a href="{{ url_for('genius_page') }}">Find Gigs</a>
                    <a href="#" id="myApplicationsLink">My Applications</a>

                    <!-- Contracts Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Contracts
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">Log Hours</a>
                            <a href="{{ url_for('landing_page') }}">Work Diary</a>
                        </div>
                    </div>

                    <!-- Earnings Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Earnings
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">Billings and Earnings</a>
                            <a href="{{ url_for('landing_page') }}">Withdraw Earnings</a>
                            <a href="{{ url_for('landing_page') }}">Tax Info</a>
                        </div>
                    </div>

                    <a href="{{ url_for('messages') }}">Messages</a>
                </div>
            </div>
            <div class="right-section">
                <div class="search-container">
                    <div class="search-bar">
                        <input type="text" id="searchInput" placeholder="Search..." onkeypress="if(event.key==='Enter') performSearch()">
                        <i class="fas fa-search icon" onclick="performSearch()"></i>
                    </div>
                </div>
                <div class="auth-buttons">
                    <div class="notification-container">
                        <div class="notification-icon" id="notification-bell" onclick="toggleNotificationDropdown()" style="cursor: pointer;">
                            <i class="fas fa-bell"></i>
                            <span id="notification-count" class="notification-badge" style="display: none;">0</span>
                        </div>
                        <div class="notification-dropdown" id="notification-dropdown">
                            <div class="notification-header">
                                <span>Notifications</span>
                                <span class="notification-header-actions" id="mark-all-read">Mark all as read</span>
                            </div>
                            <div id="notification-list">
                                <!-- Notifications will be loaded here -->
                            </div>
                            <div id="empty-notifications" class="empty-notifications" style="display: none;">
                                <i class="far fa-bell-slash"></i>
                                <h3>No notifications yet</h3>
                                <p>You'll see application updates and important notifications here</p>
                            </div>
                        </div>
                    </div>
                    <div class="profile-dropdown">
                        <div class="profile-button" onclick="toggleProfileDropdown();" style="cursor: pointer;">
                            <img id="navProfilePhoto" src="/api/profile-photo/genius/{{ genius.id }}" alt="Profile Picture" onerror="this.src='/static/img/default-avatar.png'">
                        </div>
                        <div class="profile-dropdown-content" id="profile-dropdown-content" style="position: absolute; top: 100%; right: 0; background: white; border: 1px solid #ccc; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); min-width: 200px; z-index: 10000; display: none;">
                            <a href="{{ url_for('genius_profile') }}">
                                <i class="fas fa-user"></i> My Profile
                            </a>
                            <a href="{{ url_for('landing_page') }}">
                                <i class="fas fa-cog"></i> Account Settings
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="{{ url_for('logout') }}" class="logout-option" style="color: #dc3545 !important; font-weight: bold;">
                                <i class="fas fa-sign-out-alt"></i> Log Out
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>




    <!-- Main Content -->
    <div class="body-container">
        <!-- Main Content -->
        <main class="main-content">
            <!-- Profile Section -->
            <section class="profile-section">
                <!-- Name and Title above video -->
                <div class="profile-header">
                    <h1 class="profile-name" id="profileName">{{ genius.first_name }} {{ genius.last_name }}</h1>
                    <p class="profile-title">
                        <i class="fas fa-briefcase"></i>
                        {% if genius.position %}
                            <span id="profilePosition">{{ genius.position }}</span>
                        {% endif %}
                    </p>
                </div>
                
                <div class="profile-row">
                    <!-- Video Card -->
                    <div class="video-card">
                        <div class="video-container" id="videoContainer" style="background: #000; border-radius: 16px; overflow: hidden; position: relative; height: 480px; width: 100%; padding: 0; margin: 0;">
                            <!-- Video Upload Placeholder (shown when no video) -->
                            <div class="video-placeholder" id="videoPlaceholder" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; background: #000; border: 2px dashed #333; border-radius: 16px; margin: 0; padding: 0.5rem; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.background='#111'; this.style.borderColor='#004AAD'; this.style.transform='scale(1.01)'" onmouseout="this.style.background='#000'; this.style.borderColor='#333'; this.style.transform='scale(1)'">
                                <div style="text-align: center; color: #6c757d; position: relative; z-index: 2;">
                                    <div style="background: rgba(0, 74, 173, 0.1); width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem auto;">
                                        <i class="fas fa-video" style="font-size: 1.5rem; color: #004AAD;"></i>
                                    </div>
                                    <h3 style="margin: 0 0 0.5rem 0; font-size: 1.1rem; font-weight: 600; color: #333;">Add a video introduction</h3>
                                    <p style="margin: 0 0 1rem 0; font-size: 0.85rem; opacity: 0.7; line-height: 1.4;">Show clients who you are with a personal video</p>
                                    <button class="upload-video-btn" id="uploadVideoBtn" style="background: linear-gradient(135deg, #004AAD, #0066cc); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 12px; cursor: pointer; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(0, 74, 173, 0.3);" onmouseover="this.style.background='linear-gradient(135deg, #CD208B, #e91e63)'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(205, 32, 139, 0.4)'" onmouseout="this.style.background='linear-gradient(135deg, #004AAD, #0066cc)'; this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(0, 74, 173, 0.3)'">
                                        <i class="fas fa-upload"></i> Upload Video
                                    </button>
                                    <input type="file" id="videoUpload" accept="video/*" style="display: none;">
                                </div>
                            </div>

                            <!-- Video Preview (shown when video is selected but not saved) -->
                            <div class="video-preview" id="videoPreview" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: none;">
                                <video id="previewVideo" controls style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 100%; height: 100%; object-fit: cover;">
                                    <source src="#" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>
                                <div style="position: absolute; bottom: 15px; right: 15px; display: flex; gap: 0.75rem;">
                                    <button id="cancelVideoBtn" class="video-action-btn cancel-btn" style="background: #f8f9fa; color: #6c757d; border: 2px solid #dee2e6; padding: 0.75rem 1.25rem; border-radius: 8px; cursor: pointer; font-weight: 600; font-size: 0.9rem; transition: all 0.3s ease; display: flex; align-items: center; gap: 0.5rem; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" onmouseover="this.style.background='#e9ecef'; this.style.borderColor='#adb5bd'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(0,0,0,0.15)'" onmouseout="this.style.background='#f8f9fa'; this.style.borderColor='#dee2e6'; this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(0,0,0,0.1)'">
                                        <i class="fas fa-times"></i> Cancel
                                    </button>
                                    <button id="saveVideoBtn" class="video-action-btn save-btn" style="background: linear-gradient(135deg, #004AAD 0%, #0056c7 100%); color: white; border: none; padding: 0.75rem 1.25rem; border-radius: 8px; cursor: pointer; font-weight: 600; font-size: 0.9rem; transition: all 0.3s ease; display: flex; align-items: center; gap: 0.5rem; box-shadow: 0 2px 8px rgba(0, 74, 173, 0.3);" onmouseover="this.style.background='linear-gradient(135deg, #003a8c 0%, #0047b3 100%)'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(0, 74, 173, 0.4)'" onmouseout="this.style.background='linear-gradient(135deg, #004AAD 0%, #0056c7 100%)'; this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 8px rgba(0, 74, 173, 0.3)'">
                                        <i class="fas fa-save"></i> Save Video
                                    </button>
                                </div>
                            </div>

                            <!-- Video Player (shown when video exists) -->
                            <div class="video-player" id="videoPlayer" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: none;">
                                <video id="profileVideo" controls poster="https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=800&h=450&fit=crop" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 100%; height: 100%; object-fit: cover;">
                                    <source src="#" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>

                                <!-- Video Controls -->
                                <div class="video-controls" style="position: absolute; top: 10px; right: 10px; display: flex; gap: 0.5rem;">
                                    <button class="video-control-btn" id="deleteVideoBtn" style="background: rgba(220, 53, 69, 0.9); color: white; border: none; padding: 0.5rem; border-radius: 4px; cursor: pointer;" title="Delete Video">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <button class="video-control-btn" id="replaceVideoBtn" style="background: rgba(40, 167, 69, 0.9); color: white; border: none; padding: 0.5rem; border-radius: 4px; cursor: pointer;" title="Replace Video">
                                        <i class="fas fa-upload"></i>
                                    </button>
                                </div>

                                <!-- Fullscreen Button -->
                                <button class="expand-btn" id="expandBtn" aria-label="Expand video" style="position: absolute; bottom: 10px; right: 10px; background: rgba(0, 0, 0, 0.7); color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer;">
                                    <i class="fas fa-expand"></i> Fullscreen
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Profile Card -->
                    <div class="profile-card">
                        <div class="profile-content">
                            <!-- Stats - Updated to include 4 items -->
                            <div class="profile-stats">
                                <div class="stat-item">
                                    <div class="stat-value" id="hourlyRate">
                                        {% if genius.hourly_rate %}
                                            ${{ genius.hourly_rate }}
                                        {% else %}
                                            <span style="color:#bbb;">No rate set</span>
                                        {% endif %}
                                    </div>
                                    <div class="stat-label">Hourly Rate</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="totalEarnings">$0</div>
                                    <div class="stat-label">Total Earnings</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="hired">0</div>
                                    <div class="stat-label">Hired</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="completedJobs">0</div>
                                    <div class="stat-label">Completed</div>
                                </div>
                            </div>

                            <!-- Enhanced Professional Summary -->
                            <div class="profile-summary" style="background: white; border-radius: 16px; padding: 1.5rem; box-shadow: 0 4px 20px rgba(0,0,0,0.08); border: 1px solid rgba(0,0,0,0.05); margin-bottom: 1.5rem;">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 style="margin: 0; font-size: 1.2rem; font-weight: 700; color: #1e293b; display: flex; align-items: center; gap: 0.5rem; font-family: 'Poppins', sans-serif;">
                                        <i class="fas fa-user-circle" style="color: #004AAD; font-size: 1.1rem;"></i>
                                        Professional Summary
                                    </h3>
                                    <button class="edit-btn" id="editSummaryBtn" onclick="openProfessionalSummaryModal()" style="background: linear-gradient(135deg, #004AAD, #CD208B); color: white; border: none; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.85rem; font-weight: 600; cursor: pointer; transition: all 0.2s; display: flex; align-items: center; gap: 0.4rem; box-shadow: 0 2px 8px rgba(0, 74, 173, 0.3);" onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(205, 32, 139, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 8px rgba(0, 74, 173, 0.3)'">
                                        <i class="fas fa-edit" style="font-size: 0.8rem;"></i>
                                        <span>Edit</span>
                                    </button>
                                </div>

                                <!-- Enhanced Summary Text Container -->
                                <div class="summary-container" style="position: relative;">
                                    <div class="summary-text" id="summaryText" style="color: #374151; line-height: 1.6; font-size: 0.95rem; font-family: 'Poppins', sans-serif; margin: 0; word-wrap: break-word; overflow-wrap: break-word;">
                                        {% if genius.professional_sum %}
                                            {{ genius.professional_sum }}
                                        {% else %}
                                            <span style="color: #6b7280; font-style: italic; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); padding: 1rem; border-radius: 12px; display: block; border: 2px dashed #cbd5e1; text-align: center;">
                                                <i class="fas fa-plus-circle" style="color: #004AAD; margin-right: 0.5rem;"></i>
                                                Add a professional summary to showcase your skills and experience to potential clients.
                                                <br><small style="color: #9ca3af; margin-top: 0.5rem; display: block;">Click the Edit button to get started.</small>
                                            </span>
                                        {% endif %}
                                    </div>

                                    <!-- Show More/Less Button -->
                                    <button class="show-more-btn" id="showMoreBtn" style="display: none; background: none; border: none; color: #004AAD; font-weight: 600; cursor: pointer; margin-top: 0.75rem; padding: 0.5rem 0; font-size: 0.9rem; transition: all 0.2s; font-family: 'Poppins', sans-serif;" onmouseover="this.style.color='#CD208B'" onmouseout="this.style.color='#004AAD'">
                                        <i class="fas fa-chevron-down" style="margin-right: 0.5rem; font-size: 0.8rem;"></i>
                                        <span class="show-more-text">Show More</span>
                                    </button>
                                </div>
                            </div>

                            <!-- Profile Fields -->
                            <div class="profile-fields">
                                <div class="field-group">
                                    <label>Availability</label>
                                    <input type="text" value="{% if genius.availability == 'fulltime' %}Full-Time{% elif genius.availability == 'parttime' %}Part-Time{% else %}{{ genius.availability if genius.availability else '' }}{% endif %}" readonly class="field-input" id="displayAvailability">
                                </div>
                                <div class="field-group">
                                    <label>Language</label>
                                    <input type="text" value="{{ genius.language if genius.language else 'English' }}" readonly class="field-input" id="displayLanguage">
                                </div>
                                <div class="field-group">
                                    <label>Country</label>
                                    <input type="text" value="{{ genius.country if genius.country else '' }}" readonly class="field-input" id="displayCountryField">
                                </div>
                            </div>

                            <!-- Edit Profile Button -->
                            <button class="edit-profile-btn" id="editProfileBtn" onclick="openEditProfileModal()">
                                <i class="fas fa-user-edit"></i>
                                <span>Edit Profile</span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Introduction/Portfolio and Work History Section -->
            <section class="portfolio-section">
                <div class="section-row">
                    <!-- Introduction and Portfolio -->
                    <div class="section-card">
                        <!-- Enhanced Introduction -->
                        <div class="introduction-summary" style="background: white; border-radius: 16px; padding: 1.5rem; box-shadow: 0 4px 20px rgba(0,0,0,0.08); border: 1px solid rgba(0,0,0,0.05); margin-bottom: 1.5rem;">
                            <div class="flex items-center justify-between mb-4">
                                <h3 style="margin: 0; font-size: 1.2rem; font-weight: 700; color: #1e293b; display: flex; align-items: center; gap: 0.5rem; font-family: 'Poppins', sans-serif;">
                                    <i class="fas fa-info-circle" style="color: #004AAD; font-size: 1.1rem;"></i>
                                    Introduction
                                </h3>
                                <button class="edit-btn" id="editIntroductionBtn" onclick="openIntroductionModal()" style="background: linear-gradient(135deg, #004AAD, #CD208B); color: white; border: none; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.85rem; font-weight: 600; cursor: pointer; transition: all 0.2s; display: flex; align-items: center; gap: 0.4rem; box-shadow: 0 2px 8px rgba(0, 74, 173, 0.3);" onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(205, 32, 139, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 8px rgba(0, 74, 173, 0.3)'">
                                    <i class="fas fa-edit" style="font-size: 0.8rem;"></i>
                                    <span>Edit</span>
                                </button>
                            </div>

                            <!-- Enhanced Introduction Text Container -->
                            <div class="introduction-container" style="position: relative;">
                                <div class="introduction-text" id="introductionText" style="color: #374151; line-height: 1.8; font-size: 1rem; font-family: 'Poppins', sans-serif; margin: 0; word-wrap: break-word; overflow-wrap: break-word; white-space: pre-wrap; background: #fafbfc; padding: 1.2rem; border-radius: 12px; border: 1px solid #e5e7eb; min-height: 120px; text-align: left;">
                                    {% if genius.introduction %}
                                        {{ genius.introduction|replace('\n', '<br>')|safe }}
                                    {% else %}
                                        <span style="color: #6b7280; font-style: italic; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); padding: 1.5rem; border-radius: 12px; display: flex; flex-direction: column; align-items: center; justify-content: center; border: 2px dashed #cbd5e1; text-align: center; min-height: 80px;">
                                            <i class="fas fa-plus-circle" style="color: #004AAD; margin-bottom: 0.5rem; font-size: 1.2rem;"></i>
                                            <strong style="color: #1e293b; margin-bottom: 0.5rem; display: block;">Add an introduction to tell clients about yourself and your expertise.</strong>
                                            <small style="color: #9ca3af; font-size: 0.85rem;">Click the Edit button to get started and showcase your skills professionally.</small>
                                        </span>
                                    {% endif %}
                                </div>

                                <!-- Show More/Less Button for Introduction -->
                                <button class="show-more-btn" id="showMoreIntroBtn" style="display: none; background: none; border: none; color: #004AAD; font-weight: 600; cursor: pointer; margin-top: 0.75rem; padding: 0.5rem 0; font-size: 0.9rem; transition: all 0.2s; font-family: 'Poppins', sans-serif;" onmouseover="this.style.color='#CD208B'" onmouseout="this.style.color='#004AAD'">
                                    <i class="fas fa-chevron-down" style="margin-right: 0.5rem; font-size: 0.8rem;"></i>
                                    <span class="show-more-text">Show More</span>
                                </button>
                            </div>
                        </div>

                        <!-- Portfolio Section - Styled like Introduction -->
                        <div style="background: white; border-radius: 16px; padding: 1.5rem; box-shadow: 0 4px 20px rgba(0,0,0,0.08); border: 1px solid rgba(0,0,0,0.05);">
                            <!-- Portfolio Header - Same style as Introduction -->
                            <div class="flex items-center justify-between mb-4">
                                <h3 style="margin: 0; font-size: 1.2rem; font-weight: 700; color: #1e293b; display: flex; align-items: center; gap: 0.5rem; font-family: 'Poppins', sans-serif;">
                                    <i class="fas fa-briefcase" style="color: #004AAD; font-size: 1.1rem;"></i>
                                    Portfolio
                                </h3>
                                <button class="add-btn" id="portfolioAddBtn" style="background: linear-gradient(135deg, #004AAD, #CD208B) !important; color: white !important; border: none !important; padding: 0.5rem 1rem !important; border-radius: 20px !important; font-size: 0.85rem !important; font-weight: 600 !important; cursor: pointer !important; transition: all 0.2s !important; display: flex !important; align-items: center !important; gap: 0.4rem !important; box-shadow: 0 2px 8px rgba(0, 74, 173, 0.3) !important; width: auto !important; height: auto !important;" onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(205, 32, 139, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 8px rgba(0, 74, 173, 0.3)'">
                                    <i class="fas fa-plus" style="font-size: 0.8rem;"></i>
                                    <span>Add</span>
                                </button>
                            </div>

                            <!-- Portfolio Tabs -->
                            <div class="portfolio-tabs" style="display: flex; gap: 0; margin-bottom: 1.5rem; background: #f8f9fa; border-radius: 12px; padding: 0.5rem; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);">
                                <button class="portfolio-tab active" data-tab="published" style="padding: 0.875rem 2rem; background: linear-gradient(135deg, #004AAD 0%, #0066cc 100%); color: white; box-shadow: 0 4px 15px rgba(0, 74, 173, 0.3); transform: translateY(-1px); border: none; font-size: 1rem; font-weight: 600; cursor: pointer; border-radius: 8px; transition: all 0.3s ease; font-family: 'Poppins', sans-serif; position: relative; flex: 1; text-align: center;" onclick="
                                    console.log('🔥 DIRECT: Published tab clicked!');
                                    document.querySelector('[data-tab=published]').classList.add('active');
                                    document.querySelector('[data-tab=drafts]').classList.remove('active');
                                    document.querySelector('[data-tab=published]').style.background='linear-gradient(135deg, #004AAD 0%, #0066cc 100%)';
                                    document.querySelector('[data-tab=published]').style.color='white';
                                    document.querySelector('[data-tab=published]').style.boxShadow='0 4px 15px rgba(0, 74, 173, 0.3)';
                                    document.querySelector('[data-tab=published]').style.transform='translateY(-1px)';
                                    document.querySelector('[data-tab=drafts]').style.background='none';
                                    document.querySelector('[data-tab=drafts]').style.color='#6c757d';
                                    document.querySelector('[data-tab=drafts]').style.boxShadow='none';
                                    document.querySelector('[data-tab=drafts]').style.transform='translateY(0)';
                                    document.getElementById('publishedContent').style.display = 'block';
                                    document.getElementById('draftsContent').style.display = 'none';
                                    console.log('✅ DIRECT: Switched to Published');
                                ">Published</button>
                                <button class="portfolio-tab" data-tab="drafts" style="padding: 0.875rem 2rem; background: none; border: none; font-size: 1rem; font-weight: 600; color: #6c757d; cursor: pointer; border-radius: 8px; transition: all 0.3s ease; font-family: 'Poppins', sans-serif; position: relative; flex: 1; text-align: center;" onmouseover="if(!this.classList.contains('active')) { this.style.background='rgba(0, 74, 173, 0.1)'; this.style.color='#004AAD'; }" onmouseout="if(!this.classList.contains('active')) { this.style.background='none'; this.style.color='#6c757d'; }" onclick="
                                    console.log('🔥 DIRECT: Drafts tab clicked!');
                                    document.querySelector('[data-tab=published]').classList.remove('active');
                                    document.querySelector('[data-tab=drafts]').classList.add('active');
                                    document.querySelector('[data-tab=drafts]').style.background='linear-gradient(135deg, #004AAD 0%, #0066cc 100%)';
                                    document.querySelector('[data-tab=drafts]').style.color='white';
                                    document.querySelector('[data-tab=drafts]').style.boxShadow='0 4px 15px rgba(0, 74, 173, 0.3)';
                                    document.querySelector('[data-tab=drafts]').style.transform='translateY(-1px)';
                                    document.querySelector('[data-tab=published]').style.background='none';
                                    document.querySelector('[data-tab=published]').style.color='#6c757d';
                                    document.querySelector('[data-tab=published]').style.boxShadow='none';
                                    document.querySelector('[data-tab=published]').style.transform='translateY(0)';
                                    document.getElementById('publishedContent').style.display = 'none';
                                    document.getElementById('draftsContent').style.display = 'block';
                                    console.log('✅ DIRECT: Switched to Drafts');
                                ">Drafts</button>
                            </div>



                    <!-- Portfolio Grid -->
                    <div class="portfolio-grid" id="portfolioGrid">
                        <!-- Published Projects (shown by default) -->
                        <div class="portfolio-content" id="publishedContent">
                            {% if genius.portfolio and genius.portfolio.published and genius.portfolio.published|length > 0 %}
                                {% for project in genius.portfolio.published %}
                                    <div class="portfolio-card" data-project-id="{{ project.id }}"
                                         onclick="event.preventDefault(); event.stopPropagation(); openPortfolioViewModal('{{ project.id }}', '{{ project.title }}', '{{ project.project_type.replace('_', ' ').title() if project.project_type else 'Project' }}', '{{ project.description|replace("'", "\\'") if project.description else '' }}', '{{ project.technologies|replace("'", "\\'") if project.technologies else '' }}', '{{ project.project_url if project.project_url else '' }}', '{{ project.your_role|replace("'", "\\'") if project.your_role else '' }}'); return false;"
                                         style="border: 1px solid #ddd; border-radius: 8px; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1); overflow: hidden; transition: transform 0.2s, box-shadow 0.2s; position: relative; margin-bottom: 1rem; cursor: pointer;">
                                        <!-- Delete Button -->
                                        <button class="delete-portfolio-btn" data-project-id="{{ project.id }}" style="position: absolute; top: 10px; right: 10px; background: rgba(255, 68, 68, 0.9); color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; font-size: 0.8rem; z-index: 9999; display: flex; align-items: center; justify-content: center; transition: all 0.2s; opacity: 0.8; pointer-events: auto;" onmouseover="this.style.opacity='1'; this.style.transform='scale(1.1)'" onmouseout="this.style.opacity='0.8'; this.style.transform='scale(1)'" onclick="event.stopPropagation(); deletePortfolio({{ project.id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <!-- Edit Button -->
                                        <button class="edit-portfolio-btn" data-project-id="{{ project.id }}" style="position: absolute; top: 10px; right: 50px; background: rgba(0, 74, 173, 0.9); color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; font-size: 0.8rem; z-index: 9999; display: flex; align-items: center; justify-content: center; transition: all 0.2s; opacity: 0.8; pointer-events: auto;" onmouseover="this.style.opacity='1'; this.style.transform='scale(1.1)'" onmouseout="this.style.opacity='0.8'; this.style.transform='scale(1)'" onclick="event.stopPropagation(); editPortfolio({{ project.id }})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <!-- Enhanced Project Header -->
                                        <div class="portfolio-card-header">
                                            <div class="portfolio-card-subtitle">{{ project.project_type.replace('_', ' ').title() if project.project_type else 'Project' }}</div>
                                            <h3 class="portfolio-card-title">{{ project.title }}</h3>
                                        </div>

                                        <!-- Enhanced Project Content -->
                                        <div class="portfolio-card-content">
                                            {% if project.description %}
                                                <div class="portfolio-card-description">{{ project.description }}</div>
                                            {% endif %}

                                            {% if project.technologies %}
                                                <div class="portfolio-skills">
                                                    <div class="portfolio-skills-label">Skills</div>
                                                    <div class="portfolio-skills-list">
                                                        {% for skill in project.technologies.split(',') %}
                                                            <span class="portfolio-skill-tag">{{ skill.strip() }}</span>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            {% endif %}
                                            <!-- Project Link -->
                                            <div style="text-align: center; margin-top: 1rem;">
                                                <button class="btn portfolio-modal-btn portfolio-modal-btn-primary"
                                                        onclick="event.stopPropagation(); openPortfolioViewModal('{{ project.id }}', '{{ project.title }}', '{{ project.project_type.replace('_', ' ').title() if project.project_type else 'Project' }}', '{{ project.description|replace("'", "\\'") if project.description else '' }}', '{{ project.technologies|replace("'", "\\'") if project.technologies else '' }}', '{{ project.project_url if project.project_url else '' }}', '{{ project.your_role|replace("'", "\\'") if project.your_role else '' }}')">
                                                    <i class="fas fa-eye" style="margin-right: 0.5rem;"></i>
                                                    View Project
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            {% else %}
                                <div class="portfolio-empty-state">
                                    <div class="portfolio-empty-icon">
                                        <i class="fas fa-folder-plus"></i>
                                    </div>
                                    <h3 class="portfolio-empty-title">No Published Projects Yet</h3>
                                    <p class="portfolio-empty-subtitle">Showcase your amazing work by adding your first project. Let potential clients see what you can do!</p>
                                    <button class="portfolio-empty-cta" onclick="
                                        const portfolioModal = document.getElementById('addPortfolioModal');
                                        if (portfolioModal) {
                                            portfolioModal.style.display = 'block';
                                            portfolioModal.style.visibility = 'visible';
                                            portfolioModal.style.opacity = '1';
                                            portfolioModal.style.zIndex = '99999';
                                            portfolioModal.classList.add('active');
                                            document.body.style.overflow = 'hidden';
                                        }
                                    ">
                                        <i class="fas fa-plus"></i>
                                        <span>Add Your First Project</span>
                                    </button>
                                </div>
                            {% endif %}
                        </div>

                        <!-- Draft Projects (hidden by default) -->
                        <div class="portfolio-content" id="draftsContent" style="display: none;">
                            {% if genius.portfolio and genius.portfolio.drafts and genius.portfolio.drafts|length > 0 %}
                                {% for project in genius.portfolio.drafts %}
                                    <div class="portfolio-card clickable-card" data-project-id="{{ project.id }}" data-project-title="{{ project.title }}" data-project-role="{{ project.your_role or '' }}" data-project-description="{{ project.description }}" data-project-skills="{{ project.technologies or '' }}" data-project-url="{{ project.project_url or '' }}" data-status="draft" style="border: 1px solid #ddd; border-radius: 8px; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1); overflow: hidden; cursor: pointer; transition: transform 0.2s, box-shadow 0.2s; position: relative;">
                                        <!-- Delete Button -->
                                        <button class="delete-portfolio-btn" data-project-id="{{ project.id }}" style="position: absolute; top: 10px; right: 10px; background: rgba(255, 68, 68, 0.9); color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; font-size: 0.8rem; z-index: 10; display: flex; align-items: center; justify-content: center; transition: all 0.2s; opacity: 0.8;" onmouseover="this.style.opacity='1'; this.style.transform='scale(1.1)'" onmouseout="this.style.opacity='0.8'; this.style.transform='scale(1)'" onclick="event.stopPropagation(); deletePortfolio({{ project.id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <!-- Project Image -->
                                        <div style="width: 100%; height: 150px; overflow: hidden; position: relative;">
                                            {% if project.project_image_filename %}
                                                <img src="{{ url_for('api_portfolio_image', project_id=project.id) }}" alt="{{ project.project_title }}" style="width: 100%; height: 100%; object-fit: cover;">
                                            {% else %}
                                                <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #f57c00 0%, #ff9800 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem;">
                                                    <i class="fas fa-image"></i>
                                                </div>
                                            {% endif %}
                                        </div>
                                        <!-- Project Title -->
                                        <div class="portfolio-card-content" style="padding: 1rem; text-align: center;">
                                            <h3 style="margin: 0 0 0.5rem 0; font-size: 1.1rem; font-weight: 600; color: #004AAD;">{{ project.title }}</h3>
                                            <span style="background: #ffecb3; color: #f57c00; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.7rem;">Draft</span>
                                        </div>
                                    </div>
                                {% endfor %}
                            {% else %}
                                <div class="portfolio-empty-state">
                                    <div class="portfolio-empty-icon">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                    <h3 class="portfolio-empty-title">No Draft Projects Yet</h3>
                                    <p class="portfolio-empty-subtitle">Your draft projects will appear here. Start creating and save as draft to work on them later!</p>
                                    <button class="portfolio-empty-cta" onclick="
                                        const draftModal = document.getElementById('addPortfolioModal');
                                        if (draftModal) {
                                            draftModal.style.display = 'block';
                                            draftModal.style.visibility = 'visible';
                                            draftModal.style.opacity = '1';
                                            draftModal.style.zIndex = '99999';
                                            draftModal.classList.add('active');
                                            document.body.style.overflow = 'hidden';
                                        }
                                    ">
                                        <i class="fas fa-plus"></i>
                                        <span>Create Your First Draft</span>
                                    </button>
                                </div>
                            {% endif %}
                            </div>
                        </div>

                            <!-- Portfolio Navigation Arrows -->
                            <div class="portfolio-navigation" style="display: flex; justify-content: center; align-items: center; gap: 1rem; padding: 1.5rem 0; margin-top: 1rem;">
                                <button class="portfolio-nav-btn prev" id="portfolioPrevBtn" style="width: 40px; height: 40px; border-radius: 50%; border: 1px solid #ddd; background: white; color: #6c757d; font-size: 0.9rem; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.3s ease; font-family: 'Poppins', sans-serif; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);" onmouseover="this.style.background='#004AAD'; this.style.color='white'; this.style.borderColor='#004AAD'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(0, 74, 173, 0.3)'" onmouseout="this.style.background='white'; this.style.color='#6c757d'; this.style.borderColor='#ddd'; this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 8px rgba(0, 0, 0, 0.1)'" onclick="navigatePortfolio('prev')">
                                    <i class="fas fa-chevron-left"></i>
                                </button>

                                <span class="portfolio-counter" id="portfolioCounter" style="color: #6c757d; font-weight: 600; font-family: 'Poppins', sans-serif; font-size: 0.9rem; min-width: 60px; text-align: center;">1 / 1</span>

                                <button class="portfolio-nav-btn next" id="portfolioNextBtn" style="width: 40px; height: 40px; border-radius: 50%; border: 1px solid #ddd; background: white; color: #6c757d; font-size: 0.9rem; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.3s ease; font-family: 'Poppins', sans-serif; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);" onmouseover="this.style.background='#004AAD'; this.style.color='white'; this.style.borderColor='#004AAD'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(0, 74, 173, 0.3)'" onmouseout="this.style.background='white'; this.style.color='#6c757d'; this.style.borderColor='#ddd'; this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 8px rgba(0, 0, 0, 0.1)'" onclick="navigatePortfolio('next')">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>

                        </div>
                    </div>

                    <!-- Work History -->
                    <div class="section-card">
                        <div class="section-content">
                            <h2 class="text-lg font-semibold mb-6">Work History</h2>

                            <!-- Work History Summary -->
                            <div class="work-history-summary">
                                {% if genius.work_history and genius.work_history|length > 0 %}
                                    {# Render work history summary dynamically #}
                                {% else %}
                                    <p style="color:#888;">No work history yet.</p>
                                {% endif %}
                            </div>

                            <!-- Skills Used -->
                            <div class="skills-section">
                                <h3>Skills used in past work</h3>
                                <div class="skills-list">
                                    <span class="skill-tag">JavaScript</span>
                                    <span class="skill-tag">React</span>
                                    <span class="skill-tag">Node.js</span>
                                    <span class="skill-tag">Python</span>
                                    <span class="skill-tag">Digital Marketing</span>
                                    <span class="skill-tag">SEO</span>
                                    <span class="skill-tag">UI/UX Design</span>
                                    <span class="skill-tag">Database Design</span>
                                </div>
                            </div>

                            <!-- Job Tabs -->
                            <div class="job-tabs">
                                <div class="tab-list">
                                    <button class="job-tab active" data-tab="completed">Completed jobs (12)</button>
                                    <button class="job-tab" data-tab="in-progress">In progress (3)</button>
                                </div>
                            </div>

                            <!-- Job List -->
                            <div class="job-list">
                                {% if genius.jobs and genius.jobs|length > 0 %}
                                    {% for job in genius.jobs %}
                                        <div class="job-item">
                                            <!-- Render job info here -->
                                        </div>
                                    {% endfor %}
                                {% else %}
                                    <div style="padding:2rem;text-align:center;color:#888;">No jobs to display yet.</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>



    <!-- Premium Enhanced Modal for Professional Summary -->
    <div class="modal" id="professionalSummaryModal">
        <div class="modal-content" style="max-width: 1200px; width: 95%; border-radius: 28px; box-shadow: 0 40px 120px rgba(0,0,0,0.2), 0 20px 60px rgba(0,0,0,0.1); backdrop-filter: blur(20px); animation: modalSlideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1); border: 1px solid rgba(255,255,255,0.3);">
            <!-- Simple Clean Header -->
            <header class="modal-header" style="background: linear-gradient(135deg, #004AAD 0%, #CD208B 100%); color: white; padding: 2rem; border-radius: 28px 28px 0 0; position: relative;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <!-- Logo & Title -->
                    <div style="display: flex; align-items: center; gap: 1rem;">
                        <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo" style="width: 45px; height: 45px; border-radius: 50%; object-fit: cover; object-position: center; background: transparent; transform: scale(1.2);">
                        <div>
                            <h2 style="margin: 0; font-size: 1.8rem; font-weight: 700; font-family: 'Poppins', sans-serif;">Edit Professional Summary</h2>
                            <p style="margin: 0; opacity: 0.9; font-size: 0.9rem;">Update your professional summary</p>
                        </div>
                    </div>

                    <!-- Simple Close Button -->
                    <button onclick="document.getElementById('professionalSummaryModal').classList.remove('active'); document.body.style.overflow = 'auto';" style="background: rgba(255,255,255,0.2); border: none; color: white; font-size: 1.2rem; cursor: pointer; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </header>

            <!-- Premium Main Content -->
            <div class="modal-body" style="padding: 3.5rem; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%); position: relative;">
                <!-- Subtle Background Pattern -->
                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 30% 70%, rgba(102, 126, 234, 0.03) 0%, transparent 50%), radial-gradient(circle at 70% 30%, rgba(240, 147, 251, 0.03) 0%, transparent 50%); pointer-events: none;"></div>

                <!-- Enhanced Progress Indicator -->
                <div style="margin-bottom: 3rem; position: relative; z-index: 1;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                        <h3 style="margin: 0; font-size: 1.5rem; font-weight: 700; color: #1e293b; font-family: 'Poppins', sans-serif; background: linear-gradient(135deg, #1e293b 0%, #475569 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Professional Summary</h3>
                        <span style="background: linear-gradient(135deg, #004AAD 0%, #CD208B 100%); color: white; padding: 0.75rem 1.5rem; border-radius: 25px; font-size: 0.9rem; font-weight: 600; box-shadow: 0 4px 15px rgba(0, 74, 173, 0.3); backdrop-filter: blur(10px);">Update your summary</span>
                    </div>
                    <div style="background: rgba(229, 231, 235, 0.8); height: 8px; border-radius: 4px; overflow: hidden; backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.3);">
                        <div style="background: linear-gradient(90deg, #004AAD 0%, #CD208B 100%); height: 100%; width: 75%; border-radius: 4px; transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 0 10px rgba(0, 74, 173, 0.5);"></div>
                    </div>
                </div>

                <!-- Form Content -->
                <div style="position: relative; z-index: 1;">
                    <div style="background: white; border-radius: 20px; padding: 2.5rem; box-shadow: 0 10px 40px rgba(0,0,0,0.1); border: 1px solid rgba(255,255,255,0.8); backdrop-filter: blur(10px);">
                        <div style="margin-bottom: 2rem;">
                            <label style="display: block; margin-bottom: 1rem; font-weight: 600; color: #374151; font-size: 1.1rem; font-family: 'Poppins', sans-serif;">Professional Summary *</label>
                            <textarea id="professionalSummaryTextarea" placeholder="Write a compelling professional summary that showcases your skills and experience..." style="width: 100%; min-height: 200px; padding: 1.5rem; border: 2px solid #e5e7eb; border-radius: 15px; font-size: 1rem; font-family: 'Poppins', sans-serif; resize: vertical; transition: all 0.3s ease; background: #fafbfc;" onfocus="this.style.borderColor='#004AAD'; this.style.boxShadow='0 0 0 3px rgba(0, 74, 173, 0.1)'" onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"></textarea>
                        </div>

                        <!-- Enhanced Action Buttons -->
                        <div style="display: flex; justify-content: flex-end; align-items: center; gap: 1rem; margin-top: 2.5rem;">
                            <button id="summaryBackBtn" onclick="document.getElementById('professionalSummaryModal').classList.remove('active'); document.body.style.overflow = 'auto';" style="padding: 0.75rem 1.5rem; background: white; color: #004AAD; border: 2px solid #004AAD; border-radius: 25px; font-size: 0.95rem; font-weight: 600; cursor: pointer; transition: all 0.2s; display: flex; align-items: center; gap: 0.5rem;" onmouseover="this.style.background='#f8fafc'; this.style.transform='translateY(-2px)'; this.style.borderColor='#CD208B'; this.style.color='#CD208B'" onmouseout="this.style.background='white'; this.style.transform='translateY(0)'; this.style.borderColor='#004AAD'; this.style.color='#004AAD'">
                                <i class="fas fa-arrow-left" style="font-size: 0.8rem;"></i>
                                Back
                            </button>
                            <button id="summaryNextBtn" onclick="saveProfessionalSummary()" style="padding: 0.75rem 2rem; background: linear-gradient(135deg, #004AAD, #CD208B); color: white; border: none; border-radius: 25px; font-size: 0.95rem; font-weight: 600; cursor: pointer; transition: all 0.2s; box-shadow: 0 4px 15px rgba(0, 74, 173, 0.3); display: flex; align-items: center; gap: 0.5rem;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(205, 32, 139, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(0, 74, 173, 0.3)'">
                                Save
                                <i class="fas fa-check" style="font-size: 0.8rem;"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Premium Enhanced Modal for Introduction -->
    <div class="modal" id="introductionModal">
        <div class="modal-content" style="max-width: 1200px; width: 95%; border-radius: 28px; box-shadow: 0 40px 120px rgba(0,0,0,0.2), 0 20px 60px rgba(0,0,0,0.1); backdrop-filter: blur(20px); animation: modalSlideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1); border: 1px solid rgba(255,255,255,0.3);">
            <!-- Simple Clean Header -->
            <header class="modal-header" style="background: linear-gradient(135deg, #004AAD 0%, #CD208B 100%); color: white; padding: 2rem; border-radius: 28px 28px 0 0; position: relative;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <!-- Logo & Title -->
                    <div style="display: flex; align-items: center; gap: 1rem;">
                        <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo" style="width: 45px; height: 45px; border-radius: 50%; object-fit: cover; object-position: center; background: transparent; transform: scale(1.2);">
                        <div>
                            <h2 style="margin: 0; font-size: 1.8rem; font-weight: 700; font-family: 'Poppins', sans-serif;">Edit Introduction</h2>
                            <p style="margin: 0; opacity: 0.9; font-size: 0.9rem;">Update your introduction</p>
                        </div>
                    </div>

                    <!-- Simple Close Button -->
                    <button onclick="document.getElementById('introductionModal').classList.remove('active'); document.body.style.overflow = 'auto';" style="background: rgba(255,255,255,0.2); border: none; color: white; font-size: 1.2rem; cursor: pointer; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </header>

            <!-- Premium Main Content -->
            <div class="modal-body" style="padding: 3.5rem; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%); position: relative;">
                <!-- Subtle Background Pattern -->
                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 30% 70%, rgba(102, 126, 234, 0.03) 0%, transparent 50%), radial-gradient(circle at 70% 30%, rgba(240, 147, 251, 0.03) 0%, transparent 50%); pointer-events: none;"></div>

                <!-- Enhanced Progress Indicator -->
                <div style="margin-bottom: 3rem; position: relative; z-index: 1;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                        <h3 style="margin: 0; font-size: 1.5rem; font-weight: 700; color: #1e293b; font-family: 'Poppins', sans-serif; background: linear-gradient(135deg, #1e293b 0%, #475569 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Introduction</h3>
                        <span style="background: linear-gradient(135deg, #004AAD 0%, #CD208B 100%); color: white; padding: 0.75rem 1.5rem; border-radius: 25px; font-size: 0.9rem; font-weight: 600; box-shadow: 0 4px 15px rgba(0, 74, 173, 0.3); backdrop-filter: blur(10px);">Update your introduction</span>
                    </div>
                    <div style="background: rgba(229, 231, 235, 0.8); height: 8px; border-radius: 4px; overflow: hidden; backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.3);">
                        <div style="background: linear-gradient(90deg, #004AAD 0%, #CD208B 100%); height: 100%; width: 70%; border-radius: 4px; transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 0 10px rgba(0, 74, 173, 0.5);"></div>
                    </div>
                </div>

                <!-- Form Content -->
                <div style="position: relative; z-index: 1;">
                    <div style="background: white; border-radius: 20px; padding: 2.5rem; box-shadow: 0 10px 40px rgba(0,0,0,0.1); border: 1px solid rgba(255,255,255,0.8); backdrop-filter: blur(10px);">
                        <div style="margin-bottom: 2rem;">
                            <label style="display: block; margin-bottom: 1rem; font-weight: 600; color: #374151; font-size: 1.1rem; font-family: 'Poppins', sans-serif;">Introduction *</label>
                            <textarea id="introductionTextarea" placeholder="Write your introduction..." style="width: 100%; min-height: 250px; padding: 1.5rem; border: 2px solid #e5e7eb; border-radius: 15px; font-size: 1rem; font-family: 'Poppins', sans-serif; resize: vertical; transition: all 0.3s ease; background: #fafbfc;" onfocus="this.style.borderColor='#004AAD'; this.style.boxShadow='0 0 0 3px rgba(0, 74, 173, 0.1)'" onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">{{ genius.introduction or '' }}</textarea>
                        </div>

                        <!-- Enhanced Action Buttons -->
                        <div style="display: flex; justify-content: flex-end; align-items: center; gap: 1rem; margin-top: 2.5rem;">
                            <button id="introBackBtn" onclick="document.getElementById('introductionModal').classList.remove('active'); document.body.style.overflow = 'auto';" style="padding: 0.75rem 1.5rem; background: white; color: #004AAD; border: 2px solid #004AAD; border-radius: 25px; font-size: 0.95rem; font-weight: 600; cursor: pointer; transition: all 0.2s; display: flex; align-items: center; gap: 0.5rem;" onmouseover="this.style.background='#f8fafc'; this.style.transform='translateY(-2px)'; this.style.borderColor='#CD208B'; this.style.color='#CD208B'" onmouseout="this.style.background='white'; this.style.transform='translateY(0)'; this.style.borderColor='#004AAD'; this.style.color='#004AAD'">
                                <i class="fas fa-arrow-left" style="font-size: 0.8rem;"></i>
                                Back
                            </button>
                            <button id="introNextBtn" onclick="saveIntroduction()" style="padding: 0.75rem 2rem; background: linear-gradient(135deg, #004AAD, #CD208B); color: white; border: none; border-radius: 25px; font-size: 0.95rem; font-weight: 600; cursor: pointer; transition: all 0.2s; box-shadow: 0 4px 15px rgba(0, 74, 173, 0.3); display: flex; align-items: center; gap: 0.5rem;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(205, 32, 139, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(0, 74, 173, 0.3)'">
                                Save
                                <i class="fas fa-check" style="font-size: 0.8rem;"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Premium Enhanced Modal for Edit Profile -->
    <div class="modal" id="editProfileModal">
        <div class="modal-content" style="max-width: 1200px; width: 95%; border-radius: 28px; box-shadow: 0 40px 120px rgba(0,0,0,0.2), 0 20px 60px rgba(0,0,0,0.1); backdrop-filter: blur(20px); animation: modalSlideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1); border: 1px solid rgba(255,255,255,0.3);">
            <!-- Simple Clean Header -->
            <header class="modal-header" style="background: linear-gradient(135deg, #004AAD 0%, #CD208B 100%); color: white; padding: 2rem; border-radius: 28px 28px 0 0; position: relative;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <!-- Logo & Title -->
                    <div style="display: flex; align-items: center; gap: 1rem;">
                        <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo" style="width: 45px; height: 45px; border-radius: 50%; object-fit: cover; object-position: center; background: transparent; transform: scale(1.2);">
                        <div>
                            <h2 style="margin: 0; font-size: 1.8rem; font-weight: 700; font-family: 'Poppins', sans-serif;">Edit Profile</h2>
                            <p style="margin: 0; opacity: 0.9; font-size: 0.9rem;">Update your information</p>
                        </div>
                    </div>

                    <!-- Simple Close Button -->
                    <button onclick="document.getElementById('editProfileModal').classList.remove('active'); document.body.style.overflow = 'auto';" style="background: rgba(255,255,255,0.2); border: none; color: white; font-size: 1.2rem; cursor: pointer; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </header>

            <!-- Premium Main Content -->
            <div class="modal-body" style="padding: 3.5rem; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%); position: relative;">
                <!-- Subtle Background Pattern -->
                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 30% 70%, rgba(102, 126, 234, 0.03) 0%, transparent 50%), radial-gradient(circle at 70% 30%, rgba(240, 147, 251, 0.03) 0%, transparent 50%); pointer-events: none;"></div>

                <!-- Enhanced Progress Indicator -->
                <div style="margin-bottom: 3rem; position: relative; z-index: 1;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                        <h3 style="margin: 0; font-size: 1.5rem; font-weight: 700; color: #1e293b; font-family: 'Poppins', sans-serif; background: linear-gradient(135deg, #1e293b 0%, #475569 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Profile Information</h3>
                        <span style="background: linear-gradient(135deg, #004AAD 0%, #CD208B 100%); color: white; padding: 0.75rem 1.5rem; border-radius: 25px; font-size: 0.9rem; font-weight: 600; box-shadow: 0 4px 15px rgba(0, 74, 173, 0.3); backdrop-filter: blur(10px);">Complete your profile</span>
                    </div>
                    <div style="background: rgba(229, 231, 235, 0.8); height: 8px; border-radius: 4px; overflow: hidden; backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.3);">
                        <div style="background: linear-gradient(90deg, #004AAD 0%, #CD208B 100%); height: 100%; width: 85%; border-radius: 4px; transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 0 10px rgba(0, 74, 173, 0.5);"></div>
                    </div>
                </div>

                <!-- Enhanced Profile Form -->
                <div style="display: grid; grid-template-columns: 300px 1fr; gap: 3rem; align-items: start;">
                    <!-- Enhanced Profile Photo Section -->
                    <div style="display: flex; flex-direction: column; align-items: center; gap: 1.5rem; background: white; padding: 2rem; border-radius: 16px; box-shadow: 0 8px 25px rgba(0,0,0,0.08); border: 1px solid rgba(0,0,0,0.05);">
                        <div style="position: relative;">
                            <div style="width: 180px; height: 180px; border-radius: 50%; overflow: hidden; border: 4px solid #667eea; box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3); position: relative;">
                                <img id="currentProfilePhoto" src="/api/profile-photo/genius/{{ genius.id }}" alt="Current Profile Photo" onerror="this.src='/static/img/default-avatar.png'" style="width: 100%; height: 100%; object-fit: cover;">
                            </div>
                            <!-- Photo overlay -->
                            <div style="position: absolute; bottom: 10px; right: 10px; background: #667eea; color: white; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4); transition: all 0.2s;" onclick="document.getElementById('profile-upload').click();">
                                <i class="fas fa-camera" style="font-size: 1rem;"></i>
                            </div>
                        </div>

                        <div style="text-align: center;">
                            <!-- Hidden file input -->
                            <input type="file" id="profile-upload" accept="image/*" style="display: none;">

                            <!-- Change Photo Button -->
                            <button id="changePhotoBtn" onclick="document.getElementById('profile-upload').click();" style="display: inline-flex; align-items: center; gap: 0.75rem; padding: 0.75rem 1.5rem; background: linear-gradient(135deg, #004AAD, #CD208B); color: white; border: none; border-radius: 25px; font-size: 0.9rem; font-weight: 500; cursor: pointer; transition: all 0.2s; box-shadow: 0 4px 15px rgba(0, 74, 173, 0.3); margin-bottom: 0.5rem;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(205, 32, 139, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(0, 74, 173, 0.3)'">
                                <i class="fas fa-camera"></i>
                                <span>Change Photo</span>
                            </button>

                            <!-- Save Photo Button (hidden by default) -->
                            <button id="savePhotoBtn" onclick="saveProfilePhoto()" style="display: none; align-items: center; gap: 0.75rem; padding: 0.75rem 1.5rem; background: linear-gradient(135deg, #004AAD, #CD208B); color: white; border: none; border-radius: 25px; font-size: 0.9rem; font-weight: 500; cursor: pointer; transition: all 0.2s; box-shadow: 0 4px 15px rgba(0, 74, 173, 0.3); margin-bottom: 0.5rem;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(205, 32, 139, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(0, 74, 173, 0.3)'">
                                <i class="fas fa-save"></i>
                                <span>Save Photo</span>
                            </button>

                            <p style="margin: 1rem 0 0 0; font-size: 0.8rem; color: #666; opacity: 0.8;">Maximum of 2MB</p>
                        </div>
                    </div>

                    <!-- Enhanced Profile Details Section -->
                    <div style="background: white; padding: 2.5rem; border-radius: 16px; box-shadow: 0 8px 25px rgba(0,0,0,0.08); border: 1px solid rgba(0,0,0,0.05);">
                        <h2 style="margin: 0 0 2rem 0; font-size: 1.4rem; font-weight: 600; color: #333; font-family: 'Poppins', sans-serif; display: flex; align-items: center; gap: 0.75rem;">
                            <i class="fas fa-user-edit" style="color: #667eea; font-size: 1.2rem;"></i>
                            Edit your profile
                        </h2>

                        <!-- Enhanced Form Fields -->
                        <div style="display: grid; gap: 1.5rem;">
                            <!-- Email Field -->
                            <div style="position: relative;">
                                <label for="email" style="display: block; font-size: 0.85rem; font-weight: 600; margin-bottom: 0.5rem; color: #667eea; text-transform: uppercase; letter-spacing: 0.5px; display: flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-envelope" style="font-size: 0.8rem;"></i>
                                    Email
                                </label>
                                <input type="email" id="email" value="{{ genius.email or '' }}" readonly style="width: 100%; padding: 1rem 1.25rem; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 1rem; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); color: #666; cursor: not-allowed; transition: all 0.3s;">
                            </div>

                            <!-- Mobile Field -->
                            <div style="position: relative;">
                                <label for="mobile" style="display: block; font-size: 0.85rem; font-weight: 600; margin-bottom: 0.5rem; color: #667eea; text-transform: uppercase; letter-spacing: 0.5px; display: flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-phone" style="font-size: 0.8rem;"></i>
                                    Mobile No.
                                </label>
                                <input type="tel" id="mobile" value="{{ genius.mobile or '' }}" style="width: 100%; padding: 1rem 1.25rem; border: 2px solid rgba(102, 126, 234, 0.1); border-radius: 12px; font-size: 1rem; background: rgba(255, 255, 255, 0.8); backdrop-filter: blur(10px); color: #333; transition: all 0.3s; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);" onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 4px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1)'; this.style.transform='translateY(-2px)'" onblur="this.style.borderColor='rgba(102, 126, 234, 0.1)'; this.style.boxShadow='0 4px 15px rgba(0, 0, 0, 0.05)'; this.style.transform='translateY(0)'">
                            </div>

                            <!-- Position Field -->
                            <div style="position: relative;">
                                <label for="position" style="display: block; font-size: 0.85rem; font-weight: 600; margin-bottom: 0.5rem; color: #667eea; text-transform: uppercase; letter-spacing: 0.5px; display: flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-briefcase" style="font-size: 0.8rem;"></i>
                                    Position
                                </label>
                                <input type="text" id="position" value="{{ genius.position or '' }}" style="width: 100%; padding: 1rem 1.25rem; border: 2px solid rgba(102, 126, 234, 0.1); border-radius: 12px; font-size: 1rem; background: rgba(255, 255, 255, 0.8); backdrop-filter: blur(10px); color: #333; transition: all 0.3s; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);" onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 4px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1)'; this.style.transform='translateY(-2px)'" onblur="this.style.borderColor='rgba(102, 126, 234, 0.1)'; this.style.boxShadow='0 4px 15px rgba(0, 0, 0, 0.05)'; this.style.transform='translateY(0)'">
                            </div>

                            <!-- Two Column Layout for Expertise and Rate -->
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem;">
                                <!-- Expertise Level -->
                                <div style="position: relative;">
                                    <label for="expertise" style="display: block; font-size: 0.85rem; font-weight: 600; margin-bottom: 0.5rem; color: #667eea; text-transform: uppercase; letter-spacing: 0.5px; display: flex; align-items: center; gap: 0.5rem;">
                                        <i class="fas fa-star" style="font-size: 0.8rem;"></i>
                                        Expertise Level
                                    </label>
                                    <select id="expertise" style="width: 100%; padding: 1rem 1.25rem; border: 2px solid rgba(102, 126, 234, 0.1); border-radius: 12px; font-size: 1rem; background: rgba(255, 255, 255, 0.8); backdrop-filter: blur(10px); color: #333; transition: all 0.3s; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05); cursor: pointer;" onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 4px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1)'; this.style.transform='translateY(-2px)'" onblur="this.style.borderColor='rgba(102, 126, 234, 0.1)'; this.style.boxShadow='0 4px 15px rgba(0, 0, 0, 0.05)'; this.style.transform='translateY(0)'">
                                        <option value="Expert" {% if genius.expertise == 'Expert' %}selected{% endif %}>Expert</option>
                                        <option value="Intermediate" {% if genius.expertise == 'Intermediate' %}selected{% endif %}>Intermediate</option>
                                        <option value="Beginner" {% if genius.expertise == 'Beginner' %}selected{% endif %}>Beginner</option>
                                    </select>
                                </div>

                                <!-- Rate per Hour -->
                                <div style="position: relative;">
                                    <label for="rate" style="display: block; font-size: 0.85rem; font-weight: 600; margin-bottom: 0.5rem; color: #667eea; text-transform: uppercase; letter-spacing: 0.5px; display: flex; align-items: center; gap: 0.5rem;">
                                        <i class="fas fa-dollar-sign" style="font-size: 0.8rem;"></i>
                                        Rate per Hour (USD)
                                    </label>
                                    <input type="number" id="rate" value="{{ genius.hourly_rate or '' }}" style="width: 100%; padding: 1rem 1.25rem; border: 2px solid rgba(102, 126, 234, 0.1); border-radius: 12px; font-size: 1rem; background: rgba(255, 255, 255, 0.8); backdrop-filter: blur(10px); color: #333; transition: all 0.3s; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);" onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 4px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1)'; this.style.transform='translateY(-2px)'" onblur="this.style.borderColor='rgba(102, 126, 234, 0.1)'; this.style.boxShadow='0 4px 15px rgba(0, 0, 0, 0.05)'; this.style.transform='translateY(0)'" placeholder="e.g. 25">
                                </div>
                            </div>

                            <!-- Availability Field -->
                            <div style="position: relative;">
                                <label for="availability" style="display: block; font-size: 0.85rem; font-weight: 600; margin-bottom: 0.5rem; color: #667eea; text-transform: uppercase; letter-spacing: 0.5px; display: flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-clock" style="font-size: 0.8rem;"></i>
                                    Availability
                                </label>
                                <select id="availability" style="width: 100%; padding: 1rem 1.25rem; border: 2px solid rgba(102, 126, 234, 0.1); border-radius: 12px; font-size: 1rem; background: rgba(255, 255, 255, 0.8); backdrop-filter: blur(10px); color: #333; transition: all 0.3s; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05); cursor: pointer;" onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 4px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1)'; this.style.transform='translateY(-2px)'" onblur="this.style.borderColor='rgba(102, 126, 234, 0.1)'; this.style.boxShadow='0 4px 15px rgba(0, 0, 0, 0.05)'; this.style.transform='translateY(0)'">
                                    <option value="fulltime" {% if genius.availability == 'fulltime' %}selected{% endif %}>Full-Time</option>
                                    <option value="parttime" {% if genius.availability == 'parttime' %}selected{% endif %}>Part-Time</option>
                                </select>
                            </div>

                            <!-- Two Column Layout for Country and Language -->
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem;">
                                <!-- Country Field -->
                                <div style="position: relative;">
                                    <label for="country" style="display: block; font-size: 0.85rem; font-weight: 600; margin-bottom: 0.5rem; color: #667eea; text-transform: uppercase; letter-spacing: 0.5px; display: flex; align-items: center; gap: 0.5rem;">
                                        <i class="fas fa-globe" style="font-size: 0.8rem;"></i>
                                        Country
                                    </label>
                                    <select id="country" style="width: 100%; padding: 1rem 1.25rem; border: 2px solid rgba(102, 126, 234, 0.1); border-radius: 12px; font-size: 1rem; background: rgba(255, 255, 255, 0.8); backdrop-filter: blur(10px); color: #333; transition: all 0.3s; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05); cursor: pointer;" onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 4px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1)'; this.style.transform='translateY(-2px)'" onblur="this.style.borderColor='rgba(102, 126, 234, 0.1)'; this.style.boxShadow='0 4px 15px rgba(0, 0, 0, 0.05)'; this.style.transform='translateY(0)'">
                                        <option value="">Select Country</option>
                                        <option value="Philippines" {% if genius.country == 'Philippines' %}selected{% endif %}>Philippines</option>
                                        <option value="Nigeria" {% if genius.country == 'Nigeria' %}selected{% endif %}>Nigeria</option>
                                        <option value="United States" {% if genius.country == 'United States' %}selected{% endif %}>United States</option>
                                        <option value="United Kingdom" {% if genius.country == 'United Kingdom' %}selected{% endif %}>United Kingdom</option>
                                        <option value="Canada" {% if genius.country == 'Canada' %}selected{% endif %}>Canada</option>
                                        <option value="Australia" {% if genius.country == 'Australia' %}selected{% endif %}>Australia</option>
                                        <option value="Germany" {% if genius.country == 'Germany' %}selected{% endif %}>Germany</option>
                                        <option value="France" {% if genius.country == 'France' %}selected{% endif %}>France</option>
                                        <option value="Japan" {% if genius.country == 'Japan' %}selected{% endif %}>Japan</option>
                                        <option value="China" {% if genius.country == 'China' %}selected{% endif %}>China</option>
                                        <option value="India" {% if genius.country == 'India' %}selected{% endif %}>India</option>
                                        <option value="Brazil" {% if genius.country == 'Brazil' %}selected{% endif %}>Brazil</option>
                                        <option value="South Africa" {% if genius.country == 'South Africa' %}selected{% endif %}>South Africa</option>
                                        <!-- Add a fallback option for any other country value -->
                                        {% if genius.country and genius.country not in ['Philippines', 'Nigeria', 'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 'France', 'Japan', 'China', 'India', 'Brazil', 'South Africa'] %}
                                        <option value="{{ genius.country }}" selected>{{ genius.country }}</option>
                                        {% endif %}
                                    </select>
                                </div>

                                <!-- Language Field -->
                                <div style="position: relative;">
                                    <label for="language" style="display: block; font-size: 0.85rem; font-weight: 600; margin-bottom: 0.5rem; color: #667eea; text-transform: uppercase; letter-spacing: 0.5px; display: flex; align-items: center; gap: 0.5rem;">
                                        <i class="fas fa-language" style="font-size: 0.8rem;"></i>
                                        Language
                                    </label>
                                    <select id="language" style="width: 100%; padding: 1rem 1.25rem; border: 2px solid rgba(102, 126, 234, 0.1); border-radius: 12px; font-size: 1rem; background: rgba(255, 255, 255, 0.8); backdrop-filter: blur(10px); color: #333; transition: all 0.3s; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05); cursor: pointer;" onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 4px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1)'; this.style.transform='translateY(-2px)'" onblur="this.style.borderColor='rgba(102, 126, 234, 0.1)'; this.style.boxShadow='0 4px 15px rgba(0, 0, 0, 0.05)'; this.style.transform='translateY(0)'">
                                        <option value="English" selected>English</option>
                                        <option value="French">French</option>
                                        <option value="Spanish">Spanish</option>
                                        <option value="German">German</option>
                                        <option value="Chinese">Chinese</option>
                                        <option value="Japanese">Japanese</option>
                                        <option value="Arabic">Arabic</option>
                                        <option value="Russian">Russian</option>
                                        <option value="Portuguese">Portuguese</option>
                                        <option value="Hindi">Hindi</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Enhanced Action Buttons -->
                            <div style="display: flex; justify-content: flex-end; align-items: center; gap: 1rem; margin-top: 2.5rem;">
                                <button id="profileBackBtn" onclick="closeEditProfileModal()" style="padding: 0.75rem 1.5rem; background: white; color: #004AAD; border: 2px solid #004AAD; border-radius: 25px; font-size: 0.95rem; font-weight: 600; cursor: pointer; transition: all 0.2s; display: flex; align-items: center; gap: 0.5rem;" onmouseover="this.style.background='#f8fafc'; this.style.transform='translateY(-2px)'; this.style.borderColor='#CD208B'; this.style.color='#CD208B'" onmouseout="this.style.background='white'; this.style.transform='translateY(0)'; this.style.borderColor='#004AAD'; this.style.color='#004AAD'">
                                    <i class="fas fa-arrow-left" style="font-size: 0.8rem;"></i>
                                    Back
                                </button>
                                <button id="saveProfileBtn" onclick="saveProfileData()" style="padding: 0.75rem 2rem; background: linear-gradient(135deg, #004AAD, #CD208B); color: white; border: none; border-radius: 25px; font-size: 0.95rem; font-weight: 600; cursor: pointer; transition: all 0.2s; box-shadow: 0 4px 15px rgba(0, 74, 173, 0.3); display: flex; align-items: center; gap: 0.5rem;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(205, 32, 139, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(0, 74, 173, 0.3)'">
                                    Save
                                    <i class="fas fa-check" style="font-size: 0.8rem;"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Modal for Certification Edit -->
    <div class="modal" id="certificationEditModal">
        <div class="modal-content">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content">
                    <div class="logo">
                        <div class="logo-image">
                            <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                        </div>
                        <span class="logo-text">GigGenius</span>
                    </div>
                </div>
            </header>

            <!-- Form Section -->
            <div class="modal-body">
                <h2 class="modal-title">Edit Certification *</h2>
                <textarea class="modal-textarea" placeholder="Edit your certification details..."></textarea>
                <div class="modal-buttons">
                    <button class="modal-back-btn" id="certificationBackBtn">Back</button>
                    <button class="modal-next-btn">Save Changes</button>
                </div>
            </div>
        </div>
    </div>



    <!-- Modal for Portfolio Reorder -->
    <div class="modal" id="portfolioReorderModal">
        <div class="modal-content" style="max-width: 600px;">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content" style="justify-content: space-between;">
                    <h2 style="margin: 0; font-size: 1.5rem; font-weight: 600;">Reorder portfolio projects</h2>
                    <button class="modal-close-btn" id="portfolioReorderCloseBtn" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: #666;">×</button>
                </div>
            </header>

            <!-- Reorder Section -->
            <div class="modal-body" style="padding: 2rem;">
                <div class="reorder-list">
                    <!-- Portfolio Item 1 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">Bryan's Handyman & Construction LLC</h4>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Portfolio Item 2 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">Virtual Assistant Services</h4>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Portfolio Item 3 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">Graphics Design and Web Development</h4>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Portfolio Item 4 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1551650975-87deedd944c3?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">GigGenius Web and App Development</h4>
                            <div style="background: #28a745; color: white; padding: 0.2rem 0.5rem; border-radius: 4px; font-size: 0.75rem; display: inline-block; margin-top: 0.25rem;">Reorder portfolio projects</div>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Portfolio Item 5 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">Bryan's Handyman and Construction LLC</h4>
                            <div style="color: #666; font-size: 0.875rem;">April 2024</div>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Portfolio Item 6 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">SMB Paralegal Services</h4>
                            <div style="color: #666; font-size: 0.875rem;">April 2024</div>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="modal-buttons" style="display: flex; justify-content: flex-end; gap: 1rem; margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #eee;">
                    <button class="modal-back-btn" id="portfolioReorderCancelBtn" style="background: none; border: 1px solid #ddd; color: #666; padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer;">Cancel</button>
                    <button class="modal-next-btn" id="portfolioReorderSaveBtn" style="background: #28a745; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer; font-weight: 600;">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Global variables for profile photo upload
        let selectedFile = null;
        let originalPhotoSrc = null;

        // Profile photo upload functionality
        function initializeProfilePhotoUpload() {
            const profileUpload = document.getElementById('profile-upload');
            const profilePhoto = document.getElementById('currentProfilePhoto');
            const changePhotoBtn = document.getElementById('changePhotoBtn');
            const savePhotoBtn = document.getElementById('savePhotoBtn');

            console.log('🔄 Initializing profile photo upload...', {
                profileUpload: !!profileUpload,
                profilePhoto: !!profilePhoto,
                changePhotoBtn: !!changePhotoBtn,
                savePhotoBtn: !!savePhotoBtn
            });

            if (profileUpload && profilePhoto) {
                // Store the original photo source when page loads
                originalPhotoSrc = profilePhoto.src;
                console.log('📸 Original photo source stored:', originalPhotoSrc);

                profileUpload.addEventListener('change', function(e) {
                    console.log('📁 File input changed');
                    const file = e.target.files[0];
                    if (file) {
                        console.log('📄 File selected:', file.name, file.type, file.size);

                        // Validate file type
                        if (!file.type.startsWith('image/')) {
                            showPortfolioNotification('Please select an image file (JPEG, PNG, etc.)', 'error', 'fas fa-image');
                            e.target.value = '';
                            return;
                        }

                        // Validate file size (2MB limit)
                        if (file.size > 2 * 1024 * 1024) {
                            showPortfolioNotification('File size must be less than 2MB', 'error', 'fas fa-exclamation-triangle');
                            e.target.value = '';
                            return;
                        }

                        selectedFile = file;
                        console.log('✅ File validation passed, creating preview...');

                        // Preview the new image
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            console.log('🖼️ FileReader loaded, updating photo preview...');
                            profilePhoto.src = e.target.result;

                            // Show save button and hide change button
                            if (changePhotoBtn && savePhotoBtn) {
                                changePhotoBtn.style.display = 'none';
                                savePhotoBtn.style.display = 'inline-flex';
                            }

                            console.log('✅ Profile photo preview updated successfully');
                        };
                        reader.readAsDataURL(file);
                    } else {
                        // If no file selected, revert to original photo
                        console.log('🔄 No file selected, reverting to original photo');
                        selectedFile = null;
                        profilePhoto.src = originalPhotoSrc;

                        // Hide save button and show change button
                        if (changePhotoBtn && savePhotoBtn) {
                            changePhotoBtn.style.display = 'inline-flex';
                            savePhotoBtn.style.display = 'none';
                        }
                    }
                });
            } else {
                console.error('❌ Profile upload elements not found:', {
                    profileUpload: !!profileUpload,
                    profilePhoto: !!profilePhoto
                });
            }
        }

        // Save profile photo function
        async function saveProfilePhoto() {
            if (!selectedFile) {
                showPortfolioNotification('No photo selected to save', 'error', 'fas fa-exclamation-triangle');
                return;
            }

            const savePhotoBtn = document.getElementById('savePhotoBtn');
            const changePhotoBtn = document.getElementById('changePhotoBtn');

            try {
                // Show loading state
                if (savePhotoBtn) {
                    savePhotoBtn.disabled = true;
                    savePhotoBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                }

                // Create FormData for file upload with minimal required fields
                const formData = new FormData();
                formData.append('profile_photo', selectedFile);

                // Add current profile data to avoid validation errors
                formData.append('email', '{{ genius.email }}');
                formData.append('mobile', '{{ genius.mobile }}');
                formData.append('position', '{{ genius.position }}');
                formData.append('expertise', '{{ genius.expertise }}');
                formData.append('hourly_rate', '{{ genius.hourly_rate or 0 }}');
                formData.append('availability', '{{ genius.availability }}');
                formData.append('country', '{{ genius.country }}');
                formData.append('language', '{{ genius.language or "English" }}');

                console.log('📤 Uploading profile photo...');

                const response = await fetch('/update_genius_profile', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                console.log('📤 Server response:', result);

                if (result.success) {
                    // Update all profile photos with timestamp to avoid cache
                    const timestamp = new Date().getTime();
                    const currentProfilePhoto = document.getElementById('currentProfilePhoto');
                    const navProfilePhoto = document.getElementById('navProfilePhoto');

                    if (currentProfilePhoto) {
                        currentProfilePhoto.src = `/api/profile-photo/genius/{{ genius.id }}?t=${timestamp}`;
                        originalPhotoSrc = currentProfilePhoto.src;
                    }
                    if (navProfilePhoto) {
                        navProfilePhoto.src = `/api/profile-photo/genius/{{ genius.id }}?t=${timestamp}`;
                    }

                    // Reset state
                    selectedFile = null;
                    const profileUpload = document.getElementById('profile-upload');
                    if (profileUpload) {
                        profileUpload.value = '';
                    }

                    // Show change button and hide save button
                    if (changePhotoBtn && savePhotoBtn) {
                        changePhotoBtn.style.display = 'inline-flex';
                        savePhotoBtn.style.display = 'none';
                    }

                    showPortfolioNotification('Profile photo updated successfully!', 'success', 'fas fa-camera');
                    console.log('✅ Profile photo saved successfully');
                } else {
                    showPortfolioNotification('Error updating profile photo: ' + (result.message || 'Unknown error'), 'error', 'fas fa-exclamation-triangle');
                }
            } catch (error) {
                console.error('Error saving profile photo:', error);
                showPortfolioNotification('Error saving profile photo. Please try again.', 'error', 'fas fa-exclamation-circle');
            } finally {
                // Reset button state
                if (savePhotoBtn) {
                    savePhotoBtn.disabled = false;
                    savePhotoBtn.innerHTML = '<i class="fas fa-save"></i> Save Photo';
                }
            }
        }

        // Simple Toast Notification Function
        window.showPortfolioNotification = function(message, type = 'success', icon = 'fas fa-check') {
            console.log('🔔 Toast notification called:', message, type, icon);

            // Remove any existing notifications
            const existingNotifications = document.querySelectorAll('.toast-notification');
            existingNotifications.forEach(notif => notif.remove());
            console.log('🗑️ Removed existing notifications:', existingNotifications.length);

            // Create notification element
            const toast = document.createElement('div');
            toast.className = 'toast-notification';

            // Set background with gradient and white overlay based on type
            let bgGradient = 'linear-gradient(135deg, #28a745, #20c997)'; // green gradient for success
            if (type === 'error') bgGradient = 'linear-gradient(135deg, #dc3545, #e74c3c)'; // red gradient for error
            if (type === 'info') bgGradient = 'linear-gradient(135deg, #17a2b8, #20c997)'; // blue gradient for info
            if (type === 'warning') bgGradient = 'linear-gradient(135deg, #ffc107, #ffeb3b)'; // yellow gradient for warning

            toast.style.cssText = `
                position: fixed !important;
                bottom: 20px !important;
                right: 20px !important;
                background: ${bgGradient} !important;
                color: white !important;
                padding: 15px 20px !important;
                border-radius: 12px !important;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
                z-index: 999999 !important;
                font-family: 'Poppins', sans-serif !important;
                font-size: 14px !important;
                max-width: 350px !important;
                min-width: 250px !important;
                display: flex !important;
                align-items: center !important;
                gap: 10px !important;
                animation: slideInRight 0.3s ease-out !important;
                backdrop-filter: blur(10px) !important;
                border: 1px solid rgba(255,255,255,0.2) !important;
                overflow: hidden !important;
                pointer-events: auto !important;
                visibility: visible !important;
                opacity: 1 !important;
            `;

            // Add white overlay effect
            toast.style.background = `
                ${bgGradient},
                linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)
            `;

            toast.innerHTML = `
                <div style="background: rgba(255,255,255,0.2); padding: 8px; border-radius: 50%; display: flex; align-items: center; justify-content: center; min-width: 32px; height: 32px;">
                    <i class="${icon}" style="font-size: 14px;"></i>
                </div>
                <span style="flex: 1; font-weight: 500;">${message}</span>
                <button onclick="this.parentElement.remove()" style="background: rgba(255,255,255,0.2); border: none; color: white; cursor: pointer; font-size: 14px; padding: 4px; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; transition: background 0.2s;" onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">×</button>
            `;

            // Add CSS animation if not already added
            if (!document.querySelector('#toast-styles')) {
                const style = document.createElement('style');
                style.id = 'toast-styles';
                style.textContent = `
                    @keyframes slideInRight {
                        from {
                            transform: translateX(100%) scale(0.8);
                            opacity: 0;
                        }
                        to {
                            transform: translateX(0) scale(1);
                            opacity: 1;
                        }
                    }
                    @keyframes slideOutRight {
                        from {
                            transform: translateX(0) scale(1);
                            opacity: 1;
                        }
                        to {
                            transform: translateX(100%) scale(0.8);
                            opacity: 0;
                        }
                    }
                    .toast-notification:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 12px 35px rgba(0,0,0,0.2);
                    }
                `;
                document.head.appendChild(style);
            }

            // Add to page
            document.body.appendChild(toast);
            console.log('✅ Toast notification added to DOM');
            console.log('📍 Toast position:', toast.style.position, toast.style.bottom, toast.style.right);
            console.log('🎨 Toast z-index:', toast.style.zIndex);
            console.log('👁️ Toast visibility:', toast.style.visibility, toast.style.opacity);

            // Force visibility check
            setTimeout(() => {
                const rect = toast.getBoundingClientRect();
                console.log('📐 Toast dimensions:', rect);
                console.log('🔍 Toast in viewport:', rect.width > 0 && rect.height > 0);
            }, 100);

            // Auto remove after 8 seconds (longer for testing)
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.style.animation = 'slideOutRight 0.3s ease-in';
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.parentNode.removeChild(toast);
                            console.log('🗑️ Toast notification removed');
                        }
                    }, 300);
                }
            }, 8000);
        };


    </script>
    <script>
        // Function to open professional summary modal
        function openProfessionalSummaryModal() {
            console.log('Opening professional summary modal...');
            const modal = document.getElementById('professionalSummaryModal');
            const summaryText = document.getElementById('summaryText');
            const textarea = document.getElementById('professionalSummaryTextarea');

            if (modal) {
                // Load current text into textarea
                if (summaryText && textarea) {
                    const currentText = summaryText.textContent || summaryText.innerText || '';
                    const placeholderText = 'Add a professional summary to showcase your skills and experience to potential clients. Click the Edit button to get started.';

                    if (currentText.trim() === placeholderText.trim()) {
                        textarea.value = '';
                    } else {
                        textarea.value = currentText.trim();
                    }
                }

                modal.classList.add('active');
                document.body.style.overflow = 'hidden';
                console.log('Modal should be visible now');
            } else {
                console.error('Professional summary modal not found!');
            }
        }

        // Function to save professional summary
        async function saveProfessionalSummary() {
            const saveBtn = document.getElementById('summaryNextBtn');
            const textarea = document.getElementById('professionalSummaryTextarea');

            if (!saveBtn || !textarea) return;

            try {
                // Show loading state
                saveBtn.disabled = true;
                saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';

                const formData = new FormData();
                formData.append('professional_sum', textarea.value);

                const response = await fetch('/api/update_professional_summary', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    // Update the display text
                    const summaryText = document.getElementById('summaryText');
                    if (summaryText) {
                        if (textarea.value.trim()) {
                            summaryText.textContent = textarea.value;
                            // Reinitialize the summary display with new text
                            setTimeout(() => {
                                initializeProfessionalSummary();
                            }, 100);
                        } else {
                            summaryText.innerHTML = '<span style="color: #6b7280; font-style: italic; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); padding: 1rem; border-radius: 12px; display: block; border: 2px dashed #cbd5e1; text-align: center;"><i class="fas fa-plus-circle" style="color: #004AAD; margin-right: 0.5rem;"></i>Add a professional summary to showcase your skills and experience to potential clients.<br><small style="color: #9ca3af; margin-top: 0.5rem; display: block;">Click the Edit button to get started.</small></span>';
                        }
                    }

                    // Close modal
                    document.getElementById('professionalSummaryModal').classList.remove('active');
                    document.body.style.overflow = 'auto';

                    // Show success toast notification
                    console.log('🎉 Calling showPortfolioNotification for success');
                    console.log('🔍 Function type:', typeof showPortfolioNotification);
                    console.log('🔍 Window object has function:', 'showPortfolioNotification' in window);

                    // Force call the function directly
                    try {
                        window.showPortfolioNotification('Professional summary updated successfully!', 'success', 'fas fa-user-edit');
                        console.log('✅ Toast notification called successfully');
                    } catch (error) {
                        console.error('❌ Error calling toast notification:', error);
                        alert('Professional summary updated successfully!'); // Fallback
                    }
                } else {
                    showPortfolioNotification('Error updating professional summary: ' + (result.message || 'Unknown error'), 'error', 'fas fa-exclamation-triangle');
                }
            } catch (error) {
                console.error('Error saving professional summary:', error);
                showPortfolioNotification('Error saving professional summary. Please try again.', 'error', 'fas fa-exclamation-circle');
            } finally {
                // Reset button
                saveBtn.disabled = false;
                saveBtn.innerHTML = 'Save <i class="fas fa-check" style="font-size: 0.8rem;"></i>';
            }
        }

        // Function to save introduction
        async function saveIntroduction() {
            const saveBtn = document.getElementById('introNextBtn');
            const textarea = document.getElementById('introductionTextarea');

            if (!saveBtn || !textarea) return;

            // Validate introduction content
            const introduction = textarea.value.trim();
            if (!introduction) {
                showPortfolioNotification('Introduction cannot be empty.', 'error', 'fas fa-exclamation-triangle');
                return;
            }



            try {
                // Show loading state
                saveBtn.disabled = true;
                saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';

                const formData = new FormData();
                formData.append('introduction', introduction);

                const response = await fetch('/api/update_introduction', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    // UPDATE THE CORRECT ELEMENT - introductionText (not introductionDisplay)
                    const introductionText = document.getElementById('introductionText');
                    if (introductionText) {
                        // Use innerHTML to preserve line breaks and formatting
                        introductionText.innerHTML = textarea.value.replace(/\n/g, '<br>');
                        console.log('✅ Introduction updated successfully:', textarea.value);
                    } else {
                        console.error('❌ introductionText element not found!');
                    }

                    // Close modal
                    document.getElementById('introductionModal').classList.remove('active');
                    document.body.style.overflow = 'auto';

                    // Show success modal notification
                    showPortfolioNotification('Introduction updated successfully!', 'success', 'fas fa-user-circle');
                } else {
                    showPortfolioNotification('Error updating introduction: ' + (result.message || 'Unknown error'), 'error', 'fas fa-exclamation-triangle');
                }
            } catch (error) {
                console.error('Error saving introduction:', error);
                showPortfolioNotification('Error saving introduction. Please try again.', 'error', 'fas fa-exclamation-circle');
            } finally {
                // Reset button
                saveBtn.disabled = false;
                saveBtn.innerHTML = 'Save <i class="fas fa-check" style="font-size: 0.8rem;"></i>';
            }
        }

        // Function to open introduction modal
        function openIntroductionModal() {
            console.log('Opening introduction modal...');
            const modal = document.getElementById('introductionModal');
            const introductionText = document.getElementById('introductionText');
            const textarea = document.getElementById('introductionTextarea');
            const charCount = document.getElementById('introCharCount');

            if (modal) {
                // Load current text into textarea from the new introduction text element
                if (introductionText && textarea) {
                    // Get text content and convert <br> tags back to newlines for editing
                    let currentText = introductionText.innerHTML || '';
                    currentText = currentText.replace(/<br\s*\/?>/gi, '\n');
                    console.log('Current introduction text:', currentText);

                    // Skip placeholder text and load actual content
                    if (!currentText.includes('Add an introduction to tell clients') && currentText.trim() !== '') {
                        textarea.value = currentText.trim();
                        console.log('Loaded existing introduction into textarea');
                    } else {
                        textarea.value = '';
                        console.log('No existing introduction found, starting with empty textarea');
                    }

                    // Update character count
                    if (charCount) {
                        updateCharCount(textarea, charCount);
                    }
                }

                modal.classList.add('active');
                document.body.style.overflow = 'hidden';
                console.log('Introduction modal should be visible now');
            } else {
                console.error('Introduction modal not found!');
            }
        }

        // REAL-TIME UPDATE FUNCTION - FORCE UPDATE INTRODUCTION DISPLAY
        function forceUpdateIntroduction(newText) {
            console.log('🔄 Attempting to update introduction with:', newText);

            const introductionText = document.getElementById('introductionText');
            console.log('🔍 Found introductionText element:', !!introductionText);

            if (introductionText) {
                // Clear any existing content first
                introductionText.innerHTML = '';

                // Set the new text content with proper line breaks
                introductionText.innerHTML = newText.replace(/\n/g, '<br>');

                // Force a visual update
                introductionText.style.display = 'none';
                setTimeout(() => {
                    introductionText.style.display = 'block';
                }, 10);

                console.log('✅ Introduction text updated successfully!');
                return true;
            } else {
                console.error('❌ Could not find introductionText element!');
                return false;
            }
        }

        // Function to update character count
        function updateCharCount(textarea, charCountElement) {
            if (!textarea || !charCountElement) return;

            const count = textarea.value.length;
            charCountElement.textContent = `${count} characters`;

            // Color coding based on length
            if (count < 100) {
                charCountElement.style.color = '#ef4444'; // Red - too short
            } else if (count < 300) {
                charCountElement.style.color = '#f59e0b'; // Orange - getting better
            } else if (count < 800) {
                charCountElement.style.color = '#10b981'; // Green - good length
            } else {
                charCountElement.style.color = '#6b7280'; // Gray - very long
            }
        }

        // Enhanced Professional Summary functionality - DISABLED TRUNCATION
        function initializeProfessionalSummary() {
            const summaryText = document.getElementById('summaryText');
            const showMoreBtn = document.getElementById('showMoreBtn');

            if (!summaryText) return;

            // Get the full text content
            const textContent = summaryText.textContent || summaryText.innerText || '';

            // Skip if it's the placeholder text
            if (textContent.includes('Add a professional summary to showcase')) {
                if (showMoreBtn) showMoreBtn.style.display = 'none';
                return;
            }

            const fullText = textContent.trim();

            // ALWAYS show the full text without truncation
            summaryText.textContent = fullText;

            // Hide the show more button since we're showing everything
            if (showMoreBtn) {
                showMoreBtn.style.display = 'none';
            }
        }

        // Enhanced Introduction functionality - SAME AS PROFESSIONAL SUMMARY
        function initializeIntroduction() {
            const introductionText = document.getElementById('introductionText');
            const showMoreIntroBtn = document.getElementById('showMoreIntroBtn');

            if (!introductionText) return;

            // Get the full text content
            const textContent = introductionText.textContent || introductionText.innerText || '';

            // Skip if it's the placeholder text
            if (textContent.includes('Add an introduction to tell clients')) {
                if (showMoreIntroBtn) showMoreIntroBtn.style.display = 'none';
                return;
            }

            const fullText = textContent.trim();

            // ALWAYS show the full text with proper line breaks
            if (fullText) {
                introductionText.innerHTML = fullText.replace(/\n/g, '<br>');
            }

            // Hide the show more button since we're showing everything
            if (showMoreIntroBtn) {
                showMoreIntroBtn.style.display = 'none';
            }
        }

        // Initialize on page load - multiple ways to ensure it runs
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initializing Professional Summary and Introduction...');
            initializeProfessionalSummary();
            initializeIntroduction();
        });

        // Also initialize after window loads (backup)
        window.addEventListener('load', function() {
            console.log('Window loaded - Initializing Professional Summary and Introduction...');
            setTimeout(() => {
                initializeProfessionalSummary();
                initializeIntroduction();
            }, 500);
        });

        // Video functionality
        const videoContainer = document.getElementById('videoContainer');
        const videoPlaceholder = document.getElementById('videoPlaceholder');
        const videoPreview = document.getElementById('videoPreview');
        const videoPlayer = document.getElementById('videoPlayer');
        const uploadVideoBtn = document.getElementById('uploadVideoBtn');
        const videoUpload = document.getElementById('videoUpload');
        const previewVideo = document.getElementById('previewVideo');
        const saveVideoBtn = document.getElementById('saveVideoBtn');
        const cancelVideoBtn = document.getElementById('cancelVideoBtn');
        const deleteVideoBtn = document.getElementById('deleteVideoBtn');
        const replaceVideoBtn = document.getElementById('replaceVideoBtn');
        const expandBtn = document.getElementById('expandBtn');
        const video = document.getElementById('profileVideo');

        // Check if there's an existing video from the database
        let hasVideo = false;
        let selectedVideoFile = null;

        // Show appropriate view based on video existence
        function updateVideoView() {
            if (hasVideo) {
                videoPlaceholder.style.display = 'none';
                videoPreview.style.display = 'none';
                videoPlayer.style.display = 'block';
                setTimeout(() => {
                    const video = document.getElementById('profileVideo');
                    if (video) {
                        video.style.position = 'absolute';
                        video.style.top = '0';
                        video.style.left = '0';
                        video.style.width = '100%';
                        video.style.height = '100%';
                        video.style.objectFit = 'cover';
                        video.style.borderRadius = '16px';
                        video.style.transform = 'none';
                    }
                }, 100);
            } else if (selectedVideoFile) {
                videoPlaceholder.style.display = 'none';
                videoPreview.style.display = 'block';
                videoPlayer.style.display = 'none';
                setTimeout(() => {
                    const video = document.getElementById('previewVideo');
                    if (video) {
                        video.style.position = 'absolute';
                        video.style.top = '0';
                        video.style.left = '0';
                        video.style.width = '100%';
                        video.style.height = '100%';
                        video.style.objectFit = 'cover';
                        video.style.borderRadius = '16px';
                        video.style.transform = 'none';
                    }
                }, 100);
            } else {
                videoPlaceholder.style.display = 'flex';
                videoPreview.style.display = 'none';
                videoPlayer.style.display = 'none';
            }

            // Force video styling after any view change
            setTimeout(() => {
                const videos = document.querySelectorAll('#profileVideo, #previewVideo');
                const container = document.getElementById('videoContainer');

                if (container) {
                    container.style.height = '600px';
                    container.style.position = 'relative';
                    container.style.overflow = 'hidden';
                    container.style.padding = '0';
                    container.style.margin = '0';
                }

                videos.forEach(video => {
                    if (video && video.style.display !== 'none') {
                        video.style.position = 'absolute';
                        video.style.top = '0';
                        video.style.left = '0';
                        video.style.width = '100%';
                        video.style.height = '100%';
                        video.style.objectFit = 'fill';
                        video.style.borderRadius = '16px';
                        video.style.transform = 'none';
                        video.style.minHeight = '100%';
                        video.style.maxHeight = '100%';
                        video.style.display = 'block';
                    }
                });
            }, 200);
        }

        // Check for existing video when page loads
        async function checkExistingVideo() {
            try {
                // Use the verification endpoint first
                const checkResponse = await fetch('/check_profile_video_exists');
                const checkData = await checkResponse.json();

                if (checkData.exists) {
                    // Video exists, set up the video player
                    hasVideo = true;
                    video.src = `/api/profile-video/genius/{{ genius.id }}?t=${new Date().getTime()}`;

                    // Force video styling when it loads
                    video.addEventListener('loadedmetadata', function() {
                        video.style.position = 'absolute';
                        video.style.top = '0';
                        video.style.left = '0';
                        video.style.width = '100%';
                        video.style.height = '100%';
                        video.style.objectFit = 'cover';
                        video.style.borderRadius = '16px';
                        video.style.transform = 'none';
                    });

                    console.log('✅ Existing profile video found:', checkData.filename);
                } else {
                    // No video found
                    hasVideo = false;
                    console.log('ℹ️ No existing profile video found');
                }
            } catch (error) {
                console.error('❌ Error checking existing video:', error);
                // Fallback to the old method
                try {
                    const response = await fetch(`/api/profile-video/genius/{{ genius.id }}`);
                    if (response.ok) {
                        hasVideo = true;
                        video.src = `/api/profile-video/genius/{{ genius.id }}?t=${new Date().getTime()}`;
                        console.log('✅ Existing profile video found (fallback)');
                    } else {
                        hasVideo = false;
                        console.log('ℹ️ No existing profile video found (fallback)');
                    }
                } catch (fallbackError) {
                    console.error('❌ Fallback check also failed:', fallbackError);
                    hasVideo = false;
                }
            }
            updateVideoView();
        }

        // Initialize video view
        checkExistingVideo();

        // Upload video functionality
        if (uploadVideoBtn && videoUpload) {
            uploadVideoBtn.addEventListener('click', () => {
                videoUpload.click();
            });

            videoUpload.addEventListener('change', (event) => {
                const file = event.target.files[0];
                if (file && file.type.startsWith('video/')) {
                    // Validate file size (50MB max)
                    const maxSize = 50 * 1024 * 1024; // 50MB
                    if (file.size > maxSize) {
                        showPortfolioNotification('Video file size must be less than 50MB. Current size: ' + (file.size / (1024 * 1024)).toFixed(2) + 'MB', 'error', 'fas fa-exclamation-triangle');
                        return;
                    }

                    // Validate video format
                    const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm', 'video/mkv'];
                    if (!allowedTypes.includes(file.type)) {
                        showPortfolioNotification('Please select a supported video format: MP4, AVI, MOV, WMV, WebM, or MKV', 'error', 'fas fa-file-video');
                        return;
                    }

                    selectedVideoFile = file;

                    // Show preview
                    const videoURL = URL.createObjectURL(file);
                    previewVideo.src = videoURL;
                    updateVideoView();

                    // Show file info
                    const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
                    showPortfolioNotification(`Video selected: ${file.name} (${fileSizeMB}MB)`, 'success', 'fas fa-video');
                    console.log('Video selected for preview:', file.name, 'Size:', fileSizeMB + 'MB');
                } else {
                    showPortfolioNotification('Please select a valid video file.', 'error', 'fas fa-exclamation-circle');
                }
            });

            // Add drag and drop functionality to the video placeholder
            const videoPlaceholder = document.getElementById('videoPlaceholder');

            if (videoPlaceholder) {
                // Prevent default drag behaviors
                ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                    videoPlaceholder.addEventListener(eventName, preventDefaults, false);
                });

                // Highlight drop area when item is dragged over it
                ['dragenter', 'dragover'].forEach(eventName => {
                    videoPlaceholder.addEventListener(eventName, highlight, false);
                });

                ['dragleave', 'drop'].forEach(eventName => {
                    videoPlaceholder.addEventListener(eventName, unhighlight, false);
                });

                // Handle dropped files
                videoPlaceholder.addEventListener('drop', handleDrop, false);

                function preventDefaults(e) {
                    e.preventDefault();
                    e.stopPropagation();
                }

                function highlight(e) {
                    videoPlaceholder.style.borderColor = '#007bff';
                    videoPlaceholder.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
                }

                function unhighlight(e) {
                    videoPlaceholder.style.borderColor = '#dee2e6';
                    videoPlaceholder.style.backgroundColor = '#f8f9fa';
                }

                function handleDrop(e) {
                    const dt = e.dataTransfer;
                    const files = dt.files;

                    if (files.length > 0) {
                        const file = files[0];
                        if (file && file.type.startsWith('video/')) {
                            // Check file size (50MB max)
                            const maxSize = 50 * 1024 * 1024; // 50MB
                            if (file.size > maxSize) {
                                showPortfolioNotification('Video file size must be less than 50MB. Current size: ' + (file.size / (1024 * 1024)).toFixed(2) + 'MB', 'error', 'fas fa-exclamation-triangle');
                                return;
                            }

                            // Validate video format
                            const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm', 'video/mkv'];
                            if (!allowedTypes.includes(file.type)) {
                                showPortfolioNotification('Please select a supported video format: MP4, AVI, MOV, WMV, WebM, or MKV', 'error', 'fas fa-file-video');
                                return;
                            }

                            selectedVideoFile = file;

                            // Show preview
                            const videoURL = URL.createObjectURL(file);
                            previewVideo.src = videoURL;
                            updateVideoView();

                            // Show file info
                            const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
                            showPortfolioNotification(`Video dropped and selected: ${file.name} (${fileSizeMB}MB)`, 'success', 'fas fa-video');
                            console.log('Video dropped and selected:', file.name, 'Size:', fileSizeMB + 'MB');
                        } else {
                            showPortfolioNotification('Please drop a valid video file.', 'error', 'fas fa-exclamation-circle');
                        }
                    }
                }
            }
        }

        // Save video functionality
        if (saveVideoBtn) {
            saveVideoBtn.addEventListener('click', async () => {
                if (!selectedVideoFile) {
                    showPortfolioNotification('No video selected to save', 'error', 'fas fa-exclamation-circle');
                    return;
                }

                // Double-check file size before upload
                const maxSize = 50 * 1024 * 1024; // 50MB
                if (selectedVideoFile.size > maxSize) {
                    showPortfolioNotification('Video file is too large. Please select a file smaller than 50MB.', 'error', 'fas fa-exclamation-triangle');
                    return;
                }

                // Show loading state
                saveVideoBtn.disabled = true;
                saveVideoBtn.classList.add('loading');
                saveVideoBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';

                try {
                    const formData = new FormData();
                    formData.append('video', selectedVideoFile);

                    const response = await fetch('/upload_profile_video', {
                        method: 'POST',
                        body: formData
                    });

                    const data = await response.json();
                    if (data.success) {
                        console.log('✅ Video uploaded successfully');
                        showPortfolioNotification('Profile video saved successfully!', 'success', 'fas fa-video');

                        // Move from preview to saved state
                        hasVideo = true;
                        video.src = `/api/profile-video/genius/{{ genius.id }}?t=${new Date().getTime()}`;
                        selectedVideoFile = null;
                        updateVideoView();
                    } else {
                        console.error('❌ Video upload failed:', data.error);
                        showPortfolioNotification('Error saving video: ' + data.error, 'error', 'fas fa-exclamation-triangle');
                    }
                } catch (error) {
                    console.error('❌ Video upload error:', error);
                    showPortfolioNotification('An error occurred while saving the video: ' + error.message, 'error', 'fas fa-exclamation-circle');
                } finally {
                    // Reset button state
                    saveVideoBtn.disabled = false;
                    saveVideoBtn.classList.remove('loading');
                    saveVideoBtn.innerHTML = '<i class="fas fa-save"></i> Save Video';
                }
            });
        }

        // Cancel video functionality
        if (cancelVideoBtn) {
            cancelVideoBtn.addEventListener('click', () => {
                selectedVideoFile = null;
                previewVideo.src = '';
                updateVideoView();
                console.log('Video selection cancelled');
            });
        }

        // Replace video functionality
        if (replaceVideoBtn) {
            replaceVideoBtn.addEventListener('click', () => {
                videoUpload.click();
            });
        }

        // Delete video functionality
        if (deleteVideoBtn) {
            deleteVideoBtn.addEventListener('click', () => {
                showDeleteModal();
            });
        }

        // Delete modal functions
        function showDeleteModal() {
            const modal = document.getElementById('deleteModal');
            modal.style.display = 'flex'; // Use flex to center the modal content
            document.body.style.overflow = 'hidden'; // Prevent background scrolling
        }

        function closeDeleteModal() {
            const modal = document.getElementById('deleteModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto'; // Restore scrolling
        }

        // Edit Profile Modal Function
        async function openEditProfileModal() {
            const editProfileModal = document.getElementById('editProfileModal');
            if (editProfileModal) {
                try {
                    // Reset profile photo to show current photo when modal opens
                    const currentProfilePhoto = document.getElementById('currentProfilePhoto');
                    const navProfilePhoto = document.getElementById('navProfilePhoto');
                    const profileUploadInput = document.getElementById('profile-upload');

                    if (currentProfilePhoto) {
                        // Force reload the current photo with timestamp to avoid cache
                        const timestamp = new Date().getTime();
                        currentProfilePhoto.src = `/api/profile-photo/genius/{{ genius.id }}?t=${timestamp}`;
                        // Also update navigation photo to ensure consistency
                        if (navProfilePhoto) {
                            navProfilePhoto.src = `/api/profile-photo/genius/{{ genius.id }}?t=${timestamp}`;
                        }
                        // Store the current photo as original for reset purposes
                        originalPhotoSrc = currentProfilePhoto.src;
                    }

                    // Reset file input and selected file
                    if (profileUploadInput) {
                        profileUploadInput.value = '';
                    }
                    selectedFile = null;

                    // Reset button states
                    const changePhotoBtn = document.getElementById('changePhotoBtn');
                    const savePhotoBtn = document.getElementById('savePhotoBtn');
                    if (changePhotoBtn && savePhotoBtn) {
                        changePhotoBtn.style.display = 'inline-flex';
                        savePhotoBtn.style.display = 'none';
                    }

                    // Re-initialize profile photo upload functionality
                    setTimeout(() => {
                        initializeProfilePhotoUpload();
                    }, 100);

                    // Fetch current profile data from database
                    const response = await fetch('/api/genius/profile_data');
                    const data = await response.json();

                    if (data.success && data.profile_data) {
                        const profileData = data.profile_data;

                        // Set form values with database data
                        const emailField = document.getElementById('email');
                        const mobileField = document.getElementById('mobile');
                        const positionField = document.getElementById('position');
                        const expertiseField = document.getElementById('expertise');
                        const rateField = document.getElementById('rate');
                        const availabilityField = document.getElementById('availability');
                        const countryField = document.getElementById('country');
                        const languageField = document.getElementById('language');

                        if (emailField) emailField.value = profileData.email || '';
                        if (mobileField) mobileField.value = profileData.mobile || '';
                        if (positionField) positionField.value = profileData.position || '';
                        if (expertiseField) expertiseField.value = profileData.expertise || 'Beginner';
                        if (rateField) rateField.value = profileData.hourly_rate || '';
                        if (availabilityField) availabilityField.value = profileData.availability || 'fulltime';
                        if (countryField) countryField.value = profileData.country || '';
                        if (languageField) languageField.value = profileData.language || 'English';
                    } else {
                        console.error('Failed to fetch profile data:', data.error);
                    }
                } catch (error) {
                    console.error('Error fetching profile data:', error);
                }

                editProfileModal.classList.add('active');
                document.body.style.overflow = 'hidden';
            }
        }

        // Close Edit Profile Modal Function
        function closeEditProfileModal() {
            const editProfileModal = document.getElementById('editProfileModal');
            if (editProfileModal) {
                editProfileModal.classList.remove('active');
                document.body.style.overflow = 'auto';
            }
        }

        // Save Profile Data Function
        async function saveProfileData() {
            const saveBtn = document.getElementById('saveProfileBtn');
            if (!saveBtn) return;

            try {
                // Show loading state
                saveBtn.disabled = true;
                saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';

                // Get form data
                const formData = new FormData();

                // Get all the form fields
                const emailField = document.getElementById('email');
                const mobileField = document.getElementById('mobile');
                const positionField = document.getElementById('position');
                const expertiseField = document.getElementById('expertise');
                const rateField = document.getElementById('rate');
                const availabilityField = document.getElementById('availability');
                const countryField = document.getElementById('country');
                const languageField = document.getElementById('language');

                // Add form data with proper validation
                if (emailField) formData.append('email', emailField.value || '');
                if (mobileField) formData.append('mobile', mobileField.value || '');
                if (positionField) formData.append('position', positionField.value || '');
                if (expertiseField) formData.append('expertise', expertiseField.value || 'Beginner');
                if (rateField) {
                    const rateValue = parseFloat(rateField.value) || 0;
                    formData.append('hourly_rate', rateValue);
                }
                if (availabilityField) formData.append('availability', availabilityField.value || 'fulltime');
                if (countryField) formData.append('country', countryField.value || '');
                if (languageField) formData.append('language', languageField.value || 'English');

                // Check for profile photo upload
                const profilePhotoInput = document.getElementById('profilePhoto');
                if (profilePhotoInput && profilePhotoInput.files[0]) {
                    formData.append('profile_photo', profilePhotoInput.files[0]);
                }

                // Debug: Log the form data being sent
                console.log('Sending profile data:');
                for (let [key, value] of formData.entries()) {
                    console.log(key, value);
                }

                // Send to server
                const response = await fetch('/update_genius_profile', {
                    method: 'POST',
                    body: formData
                });

                console.log('Response status:', response.status);
                const result = await response.json();
                console.log('Response data:', result);

                if (result.success) {
                    // Show success message
                    showPortfolioNotification('Profile updated successfully!', 'success', 'fas fa-check-circle');

                    // Update the display with new data
                    if (emailField && document.getElementById('displayEmailField')) {
                        document.getElementById('displayEmailField').value = emailField.value;
                    }
                    if (mobileField && document.getElementById('displayMobileField')) {
                        document.getElementById('displayMobileField').value = mobileField.value;
                    }
                    if (positionField && document.getElementById('displayPositionField')) {
                        document.getElementById('displayPositionField').value = positionField.value;
                    }
                    if (expertiseField && document.getElementById('displayExpertiseField')) {
                        document.getElementById('displayExpertiseField').value = expertiseField.value;
                    }
                    if (availabilityField && document.getElementById('displayAvailabilityField')) {
                        document.getElementById('displayAvailabilityField').value = availabilityField.value;
                    }
                    if (countryField && document.getElementById('displayCountryField')) {
                        document.getElementById('displayCountryField').value = countryField.value;
                    }

                    // Update profile photos if uploaded
                    if (result.profile_photo_updated) {
                        const timestamp = new Date().getTime();
                        const navProfilePhoto = document.getElementById('navProfilePhoto');
                        if (navProfilePhoto) {
                            navProfilePhoto.src = `/api/profile-photo/genius/{{ genius.id }}?t=${timestamp}`;
                        }
                    }

                    // Close modal
                    closeEditProfileModal();
                } else {
                    showPortfolioNotification('Error: ' + (result.error || 'Failed to update profile'), 'error', 'fas fa-exclamation-triangle');
                }

            } catch (error) {
                console.error('Error saving profile:', error);
                showPortfolioNotification('Network error: ' + error.message, 'error', 'fas fa-exclamation-circle');
            } finally {
                // Reset button
                saveBtn.disabled = false;
                saveBtn.innerHTML = 'Save <i class="fas fa-check" style="font-size: 0.8rem;"></i>';
            }
        }



        async function confirmDeleteVideo() {
            try {
                // Show loading state
                const confirmBtn = document.querySelector('.delete-modal-btn.confirm');
                const originalText = confirmBtn.textContent;
                confirmBtn.textContent = 'Deleting...';
                confirmBtn.disabled = true;

                // First, clear the video source to release file handles
                if (video) {
                    video.pause();
                    video.src = '';
                    video.load(); // Force the video element to release the file
                    video.removeAttribute('src'); // Additional cleanup
                }

                // Also clear any modal video if it exists
                const modalVideo = document.getElementById('modalVideo');
                if (modalVideo) {
                    modalVideo.pause();
                    modalVideo.src = '';
                    modalVideo.load();
                    modalVideo.removeAttribute('src');
                }

                // Clear any other video elements that might be holding the file
                const allVideos = document.querySelectorAll('video');
                allVideos.forEach(v => {
                    if (v.src && v.src.includes('profile-video')) {
                        v.pause();
                        v.src = '';
                        v.load();
                        v.removeAttribute('src');
                    }
                });

                // Wait longer for the browser to release file handles
                await new Promise(resolve => setTimeout(resolve, 500));

                const response = await fetch('/delete_profile_video', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                // Reset button state
                confirmBtn.textContent = originalText;
                confirmBtn.disabled = false;

                if (data.success) {
                    // Double-check that the video is actually gone
                    try {
                        const checkResponse = await fetch('/check_profile_video_exists');
                        const checkData = await checkResponse.json();

                        if (!checkData.exists) {
                            // Video is confirmed deleted
                            hasVideo = false;
                            updateVideoView();
                            console.log('✅ Video deleted and verified');
                            showPortfolioNotification('Profile video deleted successfully!', 'success', 'fas fa-trash-alt');
                        } else {
                            // Video still exists, but deletion was reported as successful
                            console.log('⚠️ Video deletion reported success but file still exists');
                            hasVideo = false; // Still update UI since backend says it's deleted
                            updateVideoView();
                            showPortfolioNotification('Profile video deleted successfully!', 'success', 'fas fa-trash-alt');
                        }
                    } catch (checkError) {
                        console.error('❌ Error verifying deletion:', checkError);
                        // Assume deletion was successful since backend reported success
                        hasVideo = false;
                        updateVideoView();
                        showPortfolioNotification('Profile video deleted successfully!', 'success', 'fas fa-trash-alt');
                    }
                    closeDeleteModal();
                } else {
                    console.error('❌ Video deletion failed:', data.error);
                    showPortfolioNotification('Error deleting video: ' + data.error, 'error', 'fas fa-exclamation-triangle');
                    closeDeleteModal();
                }
            } catch (error) {
                // Reset button state on error
                const confirmBtn = document.querySelector('.delete-modal-btn.confirm');
                confirmBtn.textContent = 'Delete';
                confirmBtn.disabled = false;

                console.error('❌ Video deletion error:', error);
                showPortfolioNotification('An error occurred while deleting the video: ' + error.message, 'error', 'fas fa-exclamation-circle');
                closeDeleteModal();
            }
        }

        // Close delete modal when clicking outside
        document.getElementById('deleteModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDeleteModal();
            }
        });

        // Video expand/collapse functionality
        if (expandBtn) {
            expandBtn.addEventListener('click', () => {
                videoContainer.classList.toggle('expanded');
                if (videoContainer.classList.contains('expanded')) {
                    expandBtn.innerHTML = '<i class="fas fa-compress"></i> Exit Fullscreen';
                    document.body.style.overflow = 'hidden';
                } else {
                    expandBtn.innerHTML = '<i class="fas fa-expand"></i> Fullscreen';
                    document.body.style.overflow = '';
                }
            });
        }

        // Close expanded video when clicking outside
        document.addEventListener('click', (event) => {
            if (videoContainer && videoContainer.classList.contains('expanded') &&
                !videoPlayer.contains(event.target) &&
                !expandBtn.contains(event.target)) {
                videoContainer.classList.remove('expanded');
                if (expandBtn) {
                    expandBtn.innerHTML = '<i class="fas fa-expand"></i> Fullscreen';
                }
                document.body.style.overflow = '';
            }
        });

        // Close expanded video when pressing Escape key
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && videoContainer && videoContainer.classList.contains('expanded')) {
                videoContainer.classList.remove('expanded');
                if (expandBtn) {
                    expandBtn.innerHTML = '<i class="fas fa-expand"></i> Fullscreen';
                }
                document.body.style.overflow = '';
            }
        });

        // This section has been moved to the DOMContentLoaded event below to avoid conflicts

        // Mobile Menu Toggle
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const mobileMenu = document.getElementById('mobileMenu');

        if (mobileMenuBtn && mobileMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                mobileMenu.classList.toggle('active');
                if (mobileMenu.classList.contains('active')) {
                    mobileMenuBtn.innerHTML = '<i class="fas fa-times"></i>';
                } else {
                    mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
                }
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', (event) => {
                if (!mobileMenuBtn.contains(event.target) && !mobileMenu.contains(event.target)) {
                    mobileMenu.classList.remove('active');
                    mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
                }
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded - Initializing modals...');

            // Initialize portfolio navigation after content loads
            setTimeout(() => {
                console.log('🚀 Initializing portfolio navigation...');
                initializePortfolioNavigation();
            }, 100);

            // Initialize Professional Summary and Introduction display
            console.log('Calling initializeProfessionalSummary and initializeIntroduction from main DOMContentLoaded...');
            initializeProfessionalSummary();
            initializeIntroduction();

            // Debug: Log the genius data
            console.log('Genius country value:', '{{ genius.country }}');
            console.log('Genius data:', {
                country: '{{ genius.country }}',
                availability: '{{ genius.availability }}',
                expertise: '{{ genius.expertise }}',
                position: '{{ genius.position }}'
            });



            // Modal functionality
            const professionalSummaryModal = document.getElementById('professionalSummaryModal');
            const introductionModal = document.getElementById('introductionModal');
            const editProfileModal = document.getElementById('editProfileModal');
            const portfolioViewModal = document.getElementById('portfolioViewModal');

            const portfolioReorderModal = document.getElementById('portfolioReorderModal');
            const certificationEditModal = document.getElementById('certificationEditModal');

            // Edit buttons
            const editSummary = document.getElementById('editSummaryBtn');
            const editIntroduction = document.getElementById('editIntroductionBtn');
            const editProfile = document.getElementById('editProfileBtn');
            const portfolioAdd = document.getElementById('portfolioAddBtn');
            const portfolioRefresh = document.querySelector('.portfolio-refresh-btn');
            const portfolioViewCloseBtn = document.getElementById('portfolioViewCloseBtn');
            const portfolioAddCloseBtn = document.getElementById('portfolioAddCloseBtn');
            const portfolioReorderCloseBtn = document.getElementById('portfolioReorderCloseBtn');
            const certificationEdit1 = document.getElementById('certificationEditBtn1');
            const certificationEdit2 = document.getElementById('certificationEditBtn2');

            // Back buttons
            const summaryBackBtn = document.getElementById('summaryBackBtn');
            const introBackBtn = document.getElementById('introBackBtn');
            const profileBackBtn = document.getElementById('profileBackBtn');
            const portfolioAddBackBtn = document.getElementById('portfolioAddBackBtn');

            console.log('Profile Back Button found:', profileBackBtn);
            console.log('Save Profile Button found:', document.getElementById('saveProfileBtn'));
            const portfolioReorderCancelBtn = document.getElementById('portfolioReorderCancelBtn');
            const certificationBackBtn = document.getElementById('certificationBackBtn');

            console.log('Modal elements found:', {
                professionalSummaryModal: !!professionalSummaryModal,
                editSummary: !!editSummary,
                editIntroduction: !!editIntroduction,
                editProfile: !!editProfile
            });

            // Debug: Check if elements exist
            if (!editSummary) {
                console.error('editSummaryBtn not found!');
            }
            if (!professionalSummaryModal) {
                console.error('professionalSummaryModal not found!');
            }

            // Close modals function
            function closeModal(modal) {
                if (modal) {
                    modal.classList.remove('active');
                    document.body.style.overflow = '';
                }
            }

            // Open modals - with null checks and debugging
            if (editSummary && professionalSummaryModal) {
                editSummary.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Edit Summary clicked');

                    // Load current text into textarea when opening modal
                    const summaryText = document.getElementById('summaryText');
                    const professionalSummaryTextarea = document.getElementById('professionalSummaryTextarea');

                    if (summaryText && professionalSummaryTextarea) {
                        // Get the current text, excluding the placeholder text
                        const currentText = summaryText.textContent || summaryText.innerText || '';
                        const placeholderText = 'Add a professional summary to showcase your skills and experience to potential clients. Click the Edit button to get started.';

                        if (currentText.trim() === placeholderText.trim()) {
                            professionalSummaryTextarea.value = '';
                        } else {
                            professionalSummaryTextarea.value = currentText.trim();
                        }
                    }

                    professionalSummaryModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (editIntroduction && introductionModal) {
                editIntroduction.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Edit Introduction clicked');
                    introductionModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (editProfile && editProfileModal) {
                editProfile.addEventListener('click', async (e) => {
                    e.preventDefault();

                    try {
                        // Reset profile photo to show current photo when modal opens
                        const currentProfilePhoto = document.getElementById('currentProfilePhoto');
                        const navProfilePhoto = document.getElementById('navProfilePhoto');
                        const profileUploadInput = document.getElementById('profile-upload');

                        if (currentProfilePhoto) {
                            // Force reload the current photo with timestamp to avoid cache
                            const timestamp = new Date().getTime();
                            currentProfilePhoto.src = `/api/profile-photo/genius/{{ genius.id }}?t=${timestamp}`;
                            // Also update navigation photo to ensure consistency
                            if (navProfilePhoto) {
                                navProfilePhoto.src = `/api/profile-photo/genius/{{ genius.id }}?t=${timestamp}`;
                            }
                            // Store the current photo as original for reset purposes
                            originalPhotoSrc = currentProfilePhoto.src;
                        }

                        // Reset file input and selected file
                        if (profileUploadInput) {
                            profileUploadInput.value = '';
                        }
                        selectedFile = null;

                        // Reset button states
                        const changePhotoBtn = document.getElementById('changePhotoBtn');
                        const savePhotoBtn = document.getElementById('savePhotoBtn');
                        if (changePhotoBtn && savePhotoBtn) {
                            changePhotoBtn.style.display = 'inline-flex';
                            savePhotoBtn.style.display = 'none';
                        }

                        // Re-initialize profile photo upload functionality
                        setTimeout(() => {
                            initializeProfilePhotoUpload();
                        }, 100);

                        // Fetch current profile data from database
                        const response = await fetch('/api/genius/profile_data');
                        const data = await response.json();

                        if (data.success && data.profile_data) {
                            const profileData = data.profile_data;

                            // Set form values with database data
                            const emailField = document.getElementById('email');
                            const mobileField = document.getElementById('mobile');
                            const positionField = document.getElementById('position');
                            const expertiseField = document.getElementById('expertise');
                            const rateField = document.getElementById('rate');
                            const availabilityField = document.getElementById('availability');
                            const countryField = document.getElementById('country');
                            const languageField = document.getElementById('language');

                            if (emailField) emailField.value = profileData.email || '';
                            if (mobileField) mobileField.value = profileData.mobile || '';
                            if (positionField) positionField.value = profileData.position || '';
                            if (expertiseField) expertiseField.value = profileData.expertise || 'Beginner';
                            if (rateField) rateField.value = profileData.hourly_rate || '';
                            if (availabilityField) availabilityField.value = profileData.availability || 'fulltime';
                            if (countryField) countryField.value = profileData.country || '';
                            if (languageField) languageField.value = profileData.language || 'English';
                        } else {
                            console.error('Failed to fetch profile data:', data.error);
                        }
                    } catch (error) {
                        console.error('Error fetching profile data:', error);
                    }

                    editProfileModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }



            if (portfolioRefresh && portfolioReorderModal) {
                portfolioRefresh.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Portfolio Refresh clicked');
                    portfolioReorderModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (certificationEdit1 && certificationEditModal) {
                certificationEdit1.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Certification Edit 1 clicked');
                    certificationEditModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (certificationEdit2 && certificationEditModal) {
                certificationEdit2.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Certification Edit 2 clicked');
                    certificationEditModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            // Close modals with back buttons
            if (summaryBackBtn) {
                summaryBackBtn.addEventListener('click', () => {
                    console.log('Summary back button clicked');
                    closeModal(professionalSummaryModal);
                });
            }

            if (introBackBtn) {
                introBackBtn.addEventListener('click', () => {
                    console.log('Intro back button clicked');
                    closeModal(introductionModal);
                });
            }

            if (profileBackBtn) {
                profileBackBtn.addEventListener('click', () => {
                    console.log('Profile back button clicked');
                    closeModal(editProfileModal);
                });
            }

            if (portfolioViewCloseBtn) {
                portfolioViewCloseBtn.addEventListener('click', () => {
                    console.log('Portfolio view close button clicked');
                    closeModal(portfolioViewModal);
                });
            }



            if (portfolioReorderCloseBtn) {
                portfolioReorderCloseBtn.addEventListener('click', () => {
                    console.log('Portfolio reorder close button clicked');
                    closeModal(portfolioReorderModal);
                });
            }

            if (portfolioReorderCancelBtn) {
                portfolioReorderCancelBtn.addEventListener('click', () => {
                    console.log('Portfolio reorder cancel button clicked');
                    closeModal(portfolioReorderModal);
                });
            }

            if (certificationBackBtn) {
                certificationBackBtn.addEventListener('click', () => {
                    console.log('Certification back button clicked');
                    closeModal(certificationEditModal);
                });
            }

            // Close modals when clicking outside
            window.addEventListener('click', (event) => {
                if (event.target === professionalSummaryModal) {
                    closeModal(professionalSummaryModal);
                }
                if (event.target === introductionModal) {
                    closeModal(introductionModal);
                }
                if (event.target === editProfileModal) {
                    closeModal(editProfileModal);
                }
                if (event.target === portfolioViewModal) {
                    closeModal(portfolioViewModal);
                }

                if (event.target === portfolioReorderModal) {
                    closeModal(portfolioReorderModal);
                }
                if (event.target === certificationEditModal) {
                    closeModal(certificationEditModal);
                }
            });

            // Close modals with Escape key
            document.addEventListener('keydown', (event) => {
                if (event.key === 'Escape') {
                    closeModal(professionalSummaryModal);
                    closeModal(introductionModal);
                    closeModal(editProfileModal);
                    closeModal(portfolioViewModal);

                    closeModal(portfolioReorderModal);
                    closeModal(certificationEditModal);
                }
            });

            // For mobile devices, make dropdown items clickable to expand
            const dropdownTriggers = document.querySelectorAll('.dropdown-trigger');

            dropdownTriggers.forEach(trigger => {
                trigger.addEventListener('click', function(e) {
                    if (window.innerWidth < 768) {
                        e.preventDefault();
                        const dropdown = this.nextElementSibling;
                        dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
                    }
                });
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.dropdown')) {
                    const dropdownMenus = document.querySelectorAll('.dropdown-menu');
                    dropdownMenus.forEach(menu => {
                        if (window.innerWidth < 768) {
                            menu.style.display = 'none';
                        }
                    });
                }

            });

            if (notificationBtn && notificationDropdown) {
                notificationBtn.addEventListener('click', function(event) {
                    event.stopPropagation();
                    notificationDropdown.classList.toggle('active');

                    // Close profile dropdown if open
                    if (profileDropdown && profileDropdown.classList.contains('active')) {
                        profileDropdown.classList.remove('active');
                    }
                });

                notificationDropdown.addEventListener('click', (event) => {
                    event.stopPropagation();
                });

                // Mark all notifications as read
                if (markAllReadBtn) {
                    markAllReadBtn.addEventListener('click', function() {
                        const unreadNotifications = document.querySelectorAll('.notification-item.unread');
                        unreadNotifications.forEach(notification => {
                            notification.classList.remove('unread');
                        });

                        // Remove the notification indicator
                        const indicator = document.querySelector('.notification-indicator');
                        if (indicator) {
                            indicator.style.display = 'none';
                        }
                    });
                }

                // Mark individual notification as read when clicked
                const notificationItems = document.querySelectorAll('.notification-item');
                notificationItems.forEach(item => {
                    item.addEventListener('click', function() {
                        this.classList.remove('unread');

                        // Check if there are any unread notifications left
                        const unreadNotifications = document.querySelectorAll('.notification-item.unread');
                        if (unreadNotifications.length === 0) {
                            const indicator = document.querySelector('.notification-indicator');
                            if (indicator) {
                                indicator.style.display = 'none';
                            }
                        }
                    });
                });
            }

            // Professional Summary Next Button functionality
            const summaryNextBtn = document.getElementById('summaryNextBtn');
            const professionalSummaryTextarea = document.getElementById('professionalSummaryTextarea');

            if (summaryNextBtn && professionalSummaryTextarea) {
                summaryNextBtn.addEventListener('click', async function(e) {
                    e.preventDefault();

                    const professionalSummary = professionalSummaryTextarea.value.trim();

                    if (!professionalSummary) {
                        showPortfolioNotification('Please enter a professional summary before saving.', 'error', 'fas fa-exclamation-circle');
                        return;
                    }

                    // Show loading state
                    summaryNextBtn.disabled = true;
                    summaryNextBtn.textContent = 'Saving...';

                    try {
                        const response = await fetch('/update_professional_summary', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                professional_summary: professionalSummary
                            })
                        });

                        const data = await response.json();

                        if (data.success) {
                            // Update the display text
                            const summaryText = document.getElementById('summaryText');
                            if (summaryText) {
                                if (professionalSummary.trim()) {
                                    summaryText.innerHTML = professionalSummary;
                                } else {
                                    summaryText.innerHTML = '<span style="color: #6b7280; font-style: italic;">Add a professional summary to showcase your skills and experience to potential clients. Click the Edit button to get started.</span>';
                                }
                            }

                            // Close the modal
                            closeModal(professionalSummaryModal);

                            // Show success notification
                            showPortfolioNotification('Professional summary has been updated successfully!', 'success', 'fas fa-user-edit');
                        } else {
                            showPortfolioNotification('Error: ' + (data.error || 'Failed to update professional summary'), 'error', 'fas fa-exclamation-triangle');
                        }
                    } catch (error) {
                        console.error('Error updating professional summary:', error);
                        showPortfolioNotification('An error occurred while updating your professional summary. Please try again.', 'error', 'fas fa-exclamation-circle');
                    } finally {
                        // Reset button state
                        summaryNextBtn.disabled = false;
                        summaryNextBtn.textContent = 'Next';
                    }
                });
            }

            // Introduction Next Button functionality
            const introNextBtn = document.getElementById('introNextBtn');
            const introductionTextarea = document.getElementById('introductionTextarea');
            const introCharCount = document.getElementById('introCharCount');

            // Add character counter functionality
            if (introductionTextarea && introCharCount) {
                introductionTextarea.addEventListener('input', function() {
                    updateCharCount(this, introCharCount);
                });

                // Initialize character count
                updateCharCount(introductionTextarea, introCharCount);
            }

            if (introNextBtn && introductionTextarea) {
                introNextBtn.addEventListener('click', async function(e) {
                    e.preventDefault();

                    const introduction = introductionTextarea.value.trim();
                    console.log('Introduction content:', introduction);

                    if (!introduction) {
                        showPortfolioNotification('Please enter an introduction before saving.', 'error', 'fas fa-exclamation-circle');
                        return;
                    }

                    // Show loading state
                    introNextBtn.disabled = true;
                    introNextBtn.textContent = 'Saving...';

                    try {
                        console.log('Sending request to /introduction');
                        const response = await fetch('/introduction', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                introduction: introduction
                            })
                        });

                        console.log('Response status:', response.status);
                        const data = await response.json();
                        console.log('Response data:', data);

                        if (data.success) {
                            // FORCE UPDATE the introduction display in REAL-TIME
                            const updateSuccess = forceUpdateIntroduction(introduction);
                            console.log('🔄 Force update result:', updateSuccess);

                            // Close the modal
                            closeModal(introductionModal);

                            // Show success notification
                            showPortfolioNotification('Introduction updated successfully!', 'success', 'fas fa-user-circle');
                        } else {
                            showPortfolioNotification('Error: ' + (data.error || 'Failed to update introduction'), 'error', 'fas fa-exclamation-triangle');
                        }
                    } catch (error) {
                        console.error('Error updating introduction:', error);
                        showPortfolioNotification('An error occurred while updating your introduction. Please try again.', 'error', 'fas fa-exclamation-circle');
                    } finally {
                        // Reset button state
                        introNextBtn.disabled = false;
                        introNextBtn.textContent = 'Next';
                    }
                });
            }

            // Initialize profile photo upload functionality
            initializeProfilePhotoUpload();



            // Portfolio Add Button (+ icon) functionality - Enhanced with debugging
            const portfolioAddBtn = document.getElementById('portfolioAddBtn');
            console.log('🔍 Looking for portfolioAddBtn:', portfolioAddBtn);
            if (portfolioAddBtn) {
                console.log('✅ Portfolio Add button (+ icon) found, adding event listener');
                portfolioAddBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('🔥 Portfolio Add button (+ icon) clicked!');
                    console.log('🔍 Checking if openAddPortfolioModal exists:', typeof window.openAddPortfolioModal);

                    // Try to open the modal directly
                    const modal = document.getElementById('addPortfolioModal');
                    console.log('🔍 Modal element found:', !!modal);

                    if (modal) {
                        console.log('📋 Opening modal directly...');
                        modal.style.display = 'block';
                        modal.style.visibility = 'visible';
                        modal.style.opacity = '1';
                        modal.style.zIndex = '99999';
                        modal.classList.add('active');
                        document.body.style.overflow = 'hidden';
                        console.log('✅ Modal should be visible now');
                    } else {
                        console.error('❌ Modal not found!');
                    }
                });
            } else {
                console.error('❌ Portfolio Add button (+ icon) not found');
            }



            // Profile save functionality
            const saveProfileBtn = document.getElementById('saveProfileBtn');
            if (saveProfileBtn) {
                saveProfileBtn.addEventListener('click', async (e) => {
                    e.preventDefault();
                    console.log('Edit Profile clicked - saving profile data');

                    // Get form values
                    const email = document.getElementById('email').value.trim();
                    const mobile = document.getElementById('mobile').value.trim();
                    const position = document.getElementById('position').value.trim();
                    const expertise = document.getElementById('expertise').value;
                    const hourly_rate = document.getElementById('rate').value;
                    const availability = document.getElementById('availability').value;
                    const country = document.getElementById('country').value;
                    const language = document.getElementById('language').value;

                    // Validate required fields
                    if (!email || !mobile || !position || !expertise || !hourly_rate || !availability || !country) {
                        showPortfolioNotification('Please fill in all required fields', 'error', 'fas fa-exclamation-circle');
                        return;
                    }

                    // Validate email format
                    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                    if (!emailPattern.test(email)) {
                        showPortfolioNotification('Please enter a valid email address', 'error', 'fas fa-envelope');
                        return;
                    }

                    // Validate hourly rate
                    if (isNaN(hourly_rate) || parseFloat(hourly_rate) < 0) {
                        showPortfolioNotification('Please enter a valid hourly rate', 'error', 'fas fa-dollar-sign');
                        return;
                    }

                    try {
                        // Show loading state
                        saveProfileBtn.disabled = true;
                        saveProfileBtn.textContent = 'Saving...';

                        // Check if we have a profile photo to upload
                        if (selectedFile) {
                            // Use FormData for file upload
                            const formData = new FormData();
                            formData.append('email', email);
                            formData.append('mobile', mobile);
                            formData.append('position', position);
                            formData.append('expertise', expertise);
                            formData.append('hourly_rate', parseFloat(hourly_rate));
                            formData.append('availability', availability);
                            formData.append('country', country);
                            formData.append('language', language);
                            formData.append('profile_photo', selectedFile);

                            const response = await fetch('/update_genius_profile', {
                                method: 'POST',
                                body: formData
                            });

                            const data = await response.json();

                            if (data.success) {
                                // Update all display elements immediately
                                const displayPosition = document.getElementById('displayPosition');
                                const displayCountry = document.getElementById('displayCountry');
                                const profilePosition = document.getElementById('profilePosition');
                                const displayAvailability = document.getElementById('displayAvailability');
                                const displayLanguage = document.getElementById('displayLanguage');
                                const displayCountryField = document.getElementById('displayCountryField');
                                const hourlyRateElement = document.getElementById('hourlyRate');

                                if (displayPosition) displayPosition.textContent = position;
                                if (displayCountry) displayCountry.textContent = country;
                                if (profilePosition) profilePosition.textContent = position;
                                if (displayAvailability) {
                                    const displayText = availability === 'fulltime' ? 'Full-Time' : availability === 'parttime' ? 'Part-Time' : availability;
                                    displayAvailability.value = displayText;
                                }
                                if (displayLanguage) displayLanguage.value = language;
                                if (displayCountryField) displayCountryField.value = country;
                                if (hourlyRateElement) {
                                    if (hourly_rate && hourly_rate > 0) {
                                        hourlyRateElement.textContent = `$${hourly_rate}`;
                                    } else {
                                        hourlyRateElement.textContent = 'No rate set';
                                    }
                                }

                                // Update navigation profile photo if a new photo was uploaded
                                if (selectedFile) {
                                    const timestamp = new Date().getTime();
                                    const navProfilePhoto = document.getElementById('navProfilePhoto');
                                    if (navProfilePhoto) {
                                        navProfilePhoto.src = `/api/profile-photo/genius/{{ genius.id }}?t=${timestamp}`;
                                    }
                                    // Also update the modal photo to show the saved version
                                    const currentProfilePhoto = document.getElementById('currentProfilePhoto');
                                    if (currentProfilePhoto) {
                                        currentProfilePhoto.src = `/api/profile-photo/genius/{{ genius.id }}?t=${timestamp}`;
                                    }
                                }

                                showPortfolioNotification('Profile updated successfully!', 'success', 'fas fa-user-check');
                                closeModal(editProfileModal);
                                // Don't reload the page, just update the display
                            } else {
                                showPortfolioNotification('Error: ' + (data.error || 'Failed to update profile'), 'error', 'fas fa-exclamation-triangle');
                            }
                        } else {
                            // Use JSON for regular data without file upload
                            const response = await fetch('/update_genius_profile', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    email: email,
                                    mobile: mobile,
                                    position: position,
                                    expertise: expertise,
                                    hourly_rate: parseFloat(hourly_rate),
                                    availability: availability,
                                    country: country,
                                    language: language
                                })
                            });

                            const data = await response.json();

                            if (data.success) {
                                // Update all display elements immediately
                                const displayPosition = document.getElementById('displayPosition');
                                const displayCountry = document.getElementById('displayCountry');
                                const profilePosition = document.getElementById('profilePosition');
                                const displayAvailability = document.getElementById('displayAvailability');
                                const displayLanguage = document.getElementById('displayLanguage');
                                const displayCountryField = document.getElementById('displayCountryField');
                                const hourlyRateElement = document.getElementById('hourlyRate');

                                if (displayPosition) displayPosition.textContent = position;
                                if (displayCountry) displayCountry.textContent = country;
                                if (profilePosition) profilePosition.textContent = position;
                                if (displayAvailability) {
                                    const displayText = availability === 'fulltime' ? 'Full-Time' : availability === 'parttime' ? 'Part-Time' : availability;
                                    displayAvailability.value = displayText;
                                }
                                if (displayLanguage) displayLanguage.value = language;
                                if (displayCountryField) displayCountryField.value = country;
                                if (hourlyRateElement) {
                                    if (hourly_rate && hourly_rate > 0) {
                                        hourlyRateElement.textContent = `$${hourly_rate}`;
                                    } else {
                                        hourlyRateElement.textContent = 'No rate set';
                                    }
                                }

                                showPortfolioNotification('Profile updated successfully!', 'success', 'fas fa-user-check');
                                closeModal(editProfileModal);
                                // Don't reload the page, just update the display
                            } else {
                                showPortfolioNotification('Error: ' + (data.error || 'Failed to update profile'), 'error', 'fas fa-exclamation-triangle');
                            }
                        }
                    } catch (error) {
                        showPortfolioNotification('An error occurred while saving your profile', 'error', 'fas fa-exclamation-circle');
                        console.error('Profile update error:', error);
                    } finally {
                        saveProfileBtn.disabled = false;
                        saveProfileBtn.textContent = 'Next';
                    }
                });
            }



            // Portfolio save functionality
            const portfolioAddSaveBtn = document.getElementById('portfolioAddSaveBtn');
            console.log('Portfolio save button found:', portfolioAddSaveBtn);
            if (portfolioAddSaveBtn) {
                portfolioAddSaveBtn.addEventListener('click', async (e) => {
                    e.preventDefault();
                    console.log('Portfolio Next: Preview clicked');

                    // Get form values
                    const projectTitle = document.getElementById('projectTitle').value.trim();
                    const projectRole = document.getElementById('projectRole').value.trim();
                    const projectDescription = document.getElementById('portfolioContent').value.trim();
                    const projectContent = document.getElementById('portfolioProjectContent').innerHTML.trim();
                    const projectSkills = document.getElementById('projectSkills').value.trim();
                    const relatedJob = document.getElementById('relatedJob').value.trim();

                    // Validate required fields
                    if (!projectTitle) {
                        showPortfolioNotification('Please enter a project title', 'error', 'fas fa-heading');
                        return;
                    }

                    if (!projectDescription) {
                        showPortfolioNotification('Please enter a project description', 'error', 'fas fa-align-left');
                        return;
                    }

                    try {
                        // Show loading state
                        portfolioAddSaveBtn.disabled = true;
                        portfolioAddSaveBtn.textContent = 'Saving...';

                        console.log('Saving project and publishing - Title:', projectTitle, 'Role:', projectRole, 'Description:', projectDescription);

                        // Send request to save portfolio title as published
                        const response = await fetch('/save_portfolio_title', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                project_title: projectTitle,
                                project_role: projectRole,
                                project_description: projectDescription,
                                project_content: projectContent,
                                skills_and_deliverables: projectSkills,
                                related_giggenius_job: relatedJob,
                                action: 'published'
                            })
                        });

                        console.log('Response status:', response.status);
                        const data = await response.json();
                        console.log('Response data:', data);

                        if (data.success) {
                            // Show success notification with portfolio icon
                            showPortfolioNotification('Project published successfully! Your portfolio has been updated.', 'success', 'fas fa-rocket');

                            // Reset form
                            document.getElementById('projectTitle').value = '';
                            document.getElementById('projectRole').value = '';
                            document.getElementById('projectSkills').value = '';
                            document.getElementById('relatedJob').value = '';
                            document.getElementById('portfolioContent').value = '';
                            document.getElementById('portfolioProjectContent').innerHTML = '';



                            // Navigate to Published section and reload page after notification shows
                            setTimeout(() => {
                                if (data.redirect === 'published') {
                                    // Switch to published tab
                                    const publishedTab = document.querySelector('[data-tab="published"]');
                                    if (publishedTab) {
                                        publishedTab.click();
                                    }
                                }

                                // Reload the page to show updated project title
                                window.location.reload();
                            }, 1500);
                        } else {
                            showPortfolioNotification('Error: ' + (data.error || 'Failed to save and publish project title'), 'error', 'fas fa-exclamation-triangle');
                        }
                    } catch (error) {
                        showPortfolioNotification('An error occurred while saving your project. Please try again.', 'error', 'fas fa-exclamation-circle');
                        console.error('Portfolio save error:', error);
                    } finally {
                        portfolioAddSaveBtn.disabled = false;
                        portfolioAddSaveBtn.textContent = 'Next: Preview';
                    }
                });
            }



            // Enhanced Portfolio Tabs functionality with debugging
            function initializePortfolioTabs() {
                const portfolioTabs = document.querySelectorAll('.portfolio-tab');
                const publishedContent = document.getElementById('publishedContent');
                const draftsContent = document.getElementById('draftsContent');

                console.log('🔍 Portfolio tabs found:', portfolioTabs.length);
                console.log('🔍 Published content:', !!publishedContent);
                console.log('🔍 Drafts content:', !!draftsContent);

                portfolioTabs.forEach((tab, index) => {
                    console.log(`🔍 Tab ${index}:`, tab.getAttribute('data-tab'), tab.textContent);

                    tab.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('🔥 Tab clicked:', this.getAttribute('data-tab'));

                        // Remove active class from all tabs
                        portfolioTabs.forEach(t => {
                            t.classList.remove('active');
                            console.log('🔄 Removed active from:', t.getAttribute('data-tab'));
                        });

                        // Add active class to clicked tab
                        this.classList.add('active');
                        console.log('✅ Added active to:', this.getAttribute('data-tab'));

                        // Show/hide different portfolio content
                        const tabType = this.getAttribute('data-tab');
                        console.log('🔄 Switching to tab:', tabType);

                        if (tabType === 'published') {
                            if (publishedContent) {
                                publishedContent.style.display = 'block';
                                console.log('✅ Showing published content');
                            }
                            if (draftsContent) {
                                draftsContent.style.display = 'none';
                                console.log('✅ Hiding drafts content');
                            }
                        } else if (tabType === 'drafts') {
                            if (publishedContent) {
                                publishedContent.style.display = 'none';
                                console.log('✅ Hiding published content');
                            }
                            if (draftsContent) {
                                draftsContent.style.display = 'block';
                                console.log('✅ Showing drafts content');
                            }
                        }

                        // Reset pagination when switching tabs
                        // Reinitialize portfolio navigation after tab switch
                        setTimeout(() => {
                            initializePortfolioNavigation();
                        }, 100);
                    });
                });
            }

            // Initialize tabs
            initializePortfolioTabs();

            // Backup initialization after a delay
            setTimeout(() => {
                console.log('🔄 Backup tab initialization...');
                initializePortfolioTabs();
            }, 1000);

            // Manual Draft tab test function
            window.switchToDrafts = function() {
                console.log('🔥 Manual switch to drafts triggered');
                const publishedContent = document.getElementById('publishedContent');
                const draftsContent = document.getElementById('draftsContent');
                const publishedTab = document.querySelector('[data-tab="published"]');
                const draftsTab = document.querySelector('[data-tab="drafts"]');

                if (publishedTab) publishedTab.classList.remove('active');
                if (draftsTab) draftsTab.classList.add('active');
                if (publishedContent) publishedContent.style.display = 'none';
                if (draftsContent) draftsContent.style.display = 'block';

                console.log('✅ Manual switch completed');
            };

            // Manual pagination test function
            window.testPagination = function() {
                console.log('🔥 Manual pagination test triggered');

                // Force set published tab as active
                const publishedTab = document.querySelector('[data-tab="published"]');
                if (publishedTab) {
                    publishedTab.classList.add('active');
                    console.log('✅ Set published tab as active');
                }

                // Initialize portfolio navigation
                setTimeout(() => {
                    initializePortfolioNavigation();
                }, 100);

                console.log('✅ Manual pagination test completed');
            };

            // Add direct event listeners as backup
            setTimeout(() => {
                const draftsTab = document.querySelector('[data-tab="drafts"]');
                const publishedTab = document.querySelector('[data-tab="published"]');

                if (draftsTab && !draftsTab.hasAttribute('data-backup-listener')) {
                    draftsTab.setAttribute('data-backup-listener', 'true');
                    draftsTab.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('🚀 BACKUP: Drafts tab clicked!');
                        window.switchToDrafts();
                    });
                    console.log('✅ Backup drafts listener added');
                }

                if (publishedTab && !publishedTab.hasAttribute('data-backup-listener')) {
                    publishedTab.setAttribute('data-backup-listener', 'true');
                    publishedTab.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('🚀 BACKUP: Published tab clicked!');
                        const publishedContent = document.getElementById('publishedContent');
                        const draftsContent = document.getElementById('draftsContent');

                        publishedTab.classList.add('active');
                        if (draftsTab) draftsTab.classList.remove('active');
                        if (publishedContent) publishedContent.style.display = 'block';
                        if (draftsContent) draftsContent.style.display = 'none';
                    });
                    console.log('✅ Backup published listener added');
                }
            }, 2000);





            // Initialize portfolio navigation on page load
            setTimeout(() => {
                console.log('🔄 Initializing portfolio navigation...');

                // Force set the published tab as active if no tab is active
                const publishedTab = document.querySelector('[data-tab="published"]');
                const draftsTab = document.querySelector('[data-tab="drafts"]');

                if (publishedTab && !document.querySelector('.portfolio-tab.active')) {
                    publishedTab.classList.add('active');
                    console.log('✅ Set published tab as active');
                }

                initializePortfolioNavigation();
            }, 1000);

            // Portfolio Draft Save functionality
            if (portfolioAddBackBtn) {
                portfolioAddBackBtn.addEventListener('click', async (e) => {
                    e.preventDefault();
                    console.log('Portfolio Save as Draft clicked');

                    // Get form values
                    const projectTitle = document.getElementById('projectTitle').value.trim();
                    const projectRole = document.getElementById('projectRole').value.trim();
                    const projectDescription = document.getElementById('portfolioContent').value.trim();
                    const projectContent = document.getElementById('portfolioProjectContent').innerHTML.trim();
                    const projectSkills = document.getElementById('projectSkills').value.trim();
                    const relatedJob = document.getElementById('relatedJob').value.trim();

                    // Validate required fields (only title required for draft)
                    if (!projectTitle) {
                        showPortfolioNotification('Please enter a project title to save as draft', 'error', 'fas fa-heading');
                        return;
                    }

                    try {
                        // Show loading state
                        portfolioAddBackBtn.disabled = true;
                        portfolioAddBackBtn.textContent = 'Saving...';

                        console.log('Saving project as draft - Title:', projectTitle, 'Role:', projectRole, 'Description:', projectDescription);

                        // Send request to save portfolio title
                        const response = await fetch('/save_portfolio_title', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                project_title: projectTitle,
                                project_role: projectRole,
                                project_description: projectDescription,
                                project_content: projectContent,
                                skills_and_deliverables: projectSkills,
                                related_giggenius_job: relatedJob,
                                action: 'draft'
                            })
                        });

                        console.log('Draft response status:', response.status);
                        const data = await response.json();
                        console.log('Draft response data:', data);

                        if (data.success) {
                            // Show success notification with draft icon
                            showPortfolioNotification('Project saved as draft successfully! You can publish it later.', 'success', 'fas fa-save');

                            // Reset form
                            document.getElementById('projectTitle').value = '';
                            document.getElementById('projectRole').value = '';
                            document.getElementById('projectSkills').value = '';
                            document.getElementById('relatedJob').value = '';
                            document.getElementById('portfolioContent').value = '';
                            document.getElementById('portfolioProjectContent').innerHTML = '';



                            // Navigate to Drafts section and reload page after notification shows
                            setTimeout(() => {
                                if (data.redirect === 'drafts') {
                                    // Switch to drafts tab
                                    const draftsTab = document.querySelector('[data-tab="drafts"]');
                                    if (draftsTab) {
                                        draftsTab.click();
                                    }
                                }

                                // Reload the page to show updated project title
                                window.location.reload();
                            }, 1500);
                        } else {
                            showPortfolioNotification('Error: ' + (data.error || 'Failed to save project title as draft'), 'error', 'fas fa-exclamation-triangle');
                        }
                    } catch (error) {
                        showPortfolioNotification('An error occurred while saving your project as draft. Please try again.', 'error', 'fas fa-exclamation-circle');
                        console.error('Portfolio draft save error:', error);
                    } finally {
                        portfolioAddBackBtn.disabled = false;
                        portfolioAddBackBtn.textContent = 'Save as draft';
                    }
                });
            }

            // Portfolio View/Publish functionality
            const publishPortfolioBtns = document.querySelectorAll('.publish-portfolio-btn');
            publishPortfolioBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const portfolioCard = this.closest('.portfolio-card');
                    const projectId = portfolioCard.getAttribute('data-project-id');
                    const projectTitle = portfolioCard.getAttribute('data-project-title');
                    const projectRole = portfolioCard.getAttribute('data-project-role');
                    const projectDescription = portfolioCard.getAttribute('data-project-description');
                    const projectSkills = portfolioCard.getAttribute('data-project-skills');
                    const projectUrl = portfolioCard.getAttribute('data-project-url');

                    // Use the same modal function as other portfolio views
                    openPortfolioViewModal(
                        projectId,
                        projectTitle,
                        'Project',
                        projectDescription,
                        projectSkills,
                        projectUrl,
                        projectRole
                    );

                    console.log('Portfolio view opened for:', projectTitle);
                });
            });

            // Portfolio Card Click functionality for modal view
            const clickableCards = document.querySelectorAll('.clickable-card');
            clickableCards.forEach(card => {
                card.addEventListener('click', function(e) {
                    e.preventDefault();

                    const projectId = this.getAttribute('data-project-id');
                    const projectTitle = this.getAttribute('data-project-title');
                    const projectRole = this.getAttribute('data-project-role');
                    const projectDescription = this.getAttribute('data-project-description');
                    const projectSkills = this.getAttribute('data-project-skills');
                    const projectUrl = this.getAttribute('data-project-url');
                    const status = this.getAttribute('data-status');

                    console.log('Project clicked:', {
                        id: projectId,
                        title: projectTitle,
                        role: projectRole,
                        description: projectDescription,
                        skills: projectSkills,
                        url: projectUrl,
                        status: status
                    });

                    // Use the same modal function as published portfolios
                    openPortfolioViewModal(
                        projectId,
                        projectTitle,
                        status === 'draft' ? 'Draft Project' : 'Project',
                        projectDescription,
                        projectSkills,
                        projectUrl,
                        projectRole
                    );
                });
            });

            // Fetch and display the hourly_rate from the new API route in the Hourly Rate section
            fetch('/api/genius/hourly_rate')
                .then(response => response.json())
                .then(data => {
                    const el = document.getElementById('hourlyRate');
                    if (data.success && data.hourly_rate !== undefined && data.hourly_rate !== null && data.hourly_rate !== '' && data.hourly_rate != 0) {
                        el.textContent = `$${data.hourly_rate}`;
                    } else {
                        el.textContent = 'No rate set';
                    }
                })
                .catch(() => {
                    document.getElementById('hourlyRate').textContent = 'Unavailable';
                });

        // Portfolio Delete Function
        async function deletePortfolio(projectId) {
            console.log('Delete portfolio clicked for project:', projectId);

            // Show confirmation dialog
            if (!confirm('Are you sure you want to delete this portfolio project? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch('/delete_portfolio', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        project_id: projectId
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Show success notification
                    showPortfolioNotification('Portfolio project deleted successfully!', 'success', 'fas fa-trash');

                    // Remove the portfolio card from the DOM
                    const portfolioCard = document.querySelector(`[data-project-id="${projectId}"]`);
                    if (portfolioCard) {
                        portfolioCard.style.transition = 'all 0.3s ease';
                        portfolioCard.style.opacity = '0';
                        portfolioCard.style.transform = 'scale(0.8)';

                        setTimeout(() => {
                            portfolioCard.remove();

                            // Check if there are any remaining projects in the current tab
                            const activeTab = document.querySelector('.portfolio-tab.active');
                            const currentTabContent = activeTab.getAttribute('data-tab') === 'published' ?
                                document.getElementById('publishedContent') :
                                document.getElementById('draftsContent');

                            const remainingCards = currentTabContent.querySelectorAll('.portfolio-card');
                            if (remainingCards.length === 0) {
                                const emptyMessage = activeTab.getAttribute('data-tab') === 'published' ?
                                    'No published projects yet. Click + to add your first project.' :
                                    'No draft projects yet.';
                                currentTabContent.innerHTML = `<div style="padding:2rem;text-align:center;color:#888;">${emptyMessage}</div>`;
                            }
                        }, 300);
                    }
                } else {
                    showPortfolioNotification(data.error || 'Failed to delete portfolio project', 'error', 'fas fa-exclamation-triangle');
                }
            } catch (error) {
                console.error('Error deleting portfolio:', error);
                showPortfolioNotification('An error occurred while deleting the portfolio project', 'error', 'fas fa-exclamation-triangle');
            }
        }

        // Portfolio Content Functions
        function addImageUpload() {
            console.log('Image upload button clicked');
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.multiple = true; // Allow multiple file selection
            input.style.display = 'none';

            input.onchange = function(e) {
                const files = e.target.files;
                console.log('Files selected:', files.length, 'images');

                if (files.length > 0) {
                    // Process each selected file
                    Array.from(files).forEach((file, index) => {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            console.log(`Image ${index + 1} loaded, adding content block`);
                            addContentBlock('image', {
                                src: e.target.result,
                                name: file.name,
                                size: (file.size / 1024 / 1024).toFixed(2) + ' MB'
                            });
                        };
                        reader.readAsDataURL(file);
                    });
                }
            };

            document.body.appendChild(input);
            input.click();
            document.body.removeChild(input);
        }

        function addVideoUpload() {
            console.log('Video upload button clicked');
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'video/*';
            input.multiple = true; // Allow multiple video selection
            input.style.display = 'none';

            input.onchange = function(e) {
                const files = e.target.files;
                console.log('Video files selected:', files.length, 'videos');

                if (files.length > 0) {
                    Array.from(files).forEach((file, index) => {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            console.log(`Video ${index + 1} loaded, adding content block`);
                            addContentBlock('video', {
                                src: e.target.result,
                                name: file.name,
                                size: (file.size / 1024 / 1024).toFixed(2) + ' MB'
                            });
                        };
                        reader.readAsDataURL(file);
                    });
                }
            };

            document.body.appendChild(input);
            input.click();
            document.body.removeChild(input);
        }

        function addTextBlock() {
            console.log('Text block button clicked');
            // Create text block modal
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 10000; display: flex;
                justify-content: center; align-items: center;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 8px; padding: 2rem; max-width: 600px; width: 90%;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                        <h3 style="margin: 0; color: #004AAD;">Add Text Block</h3>
                        <div style="display: flex; gap: 1rem;">
                            <button id="plainTextBtn" style="padding: 0.5rem 1rem; border: 1px solid #004AAD; background: #004AAD; color: white; border-radius: 4px; cursor: pointer;">Plain text</button>
                            <button id="markdownBtn" style="padding: 0.5rem 1rem; border: 1px solid #004AAD; background: white; color: #004AAD; border-radius: 4px; cursor: pointer;">Markdown</button>
                        </div>
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; font-weight: 600; margin-bottom: 0.5rem;">Heading</label>
                        <input type="text" id="textHeading" placeholder="Enter heading" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <textarea id="textContent" placeholder="Enter your text" style="width: 100%; height: 200px; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; resize: vertical;"></textarea>
                    </div>
                    <div style="display: flex; justify-content: flex-end; gap: 1rem;">
                        <button id="cancelTextBtn" style="padding: 0.75rem 1.5rem; border: 1px solid #ddd; background: white; color: #666; border-radius: 4px; cursor: pointer;">Cancel</button>
                        <button id="addTextBtn" style="padding: 0.75rem 1.5rem; border: none; background: #CD208B; color: white; border-radius: 4px; cursor: pointer;">Add Text</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            console.log('Text block modal created and added to DOM');

            // Event listeners
            modal.querySelector('#cancelTextBtn').onclick = () => {
                console.log('Text block cancelled');
                document.body.removeChild(modal);
            };
            modal.querySelector('#addTextBtn').onclick = () => {
                const heading = modal.querySelector('#textHeading').value;
                const content = modal.querySelector('#textContent').value;
                console.log('Adding text block:', { heading, content });
                if (content.trim()) {
                    addContentBlock('text', { heading, content });
                    document.body.removeChild(modal);
                } else {
                    showPortfolioNotification('Please enter some text content', 'error', 'fas fa-edit');
                }
            };
        }

        function addPdfUpload() {
            console.log('PDF upload button clicked');
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.pdf';
            input.style.display = 'none';

            input.onchange = function(e) {
                console.log('PDF file selected:', e.target.files[0]);
                const file = e.target.files[0];
                if (file) {
                    console.log('Adding PDF content block');
                    addContentBlock('pdf', {
                        name: file.name,
                        size: (file.size / 1024 / 1024).toFixed(2) + ' MB'
                    });
                }
            };

            document.body.appendChild(input);
            input.click();
            document.body.removeChild(input);
        }

        function addMusic() {
            console.log('Music upload button clicked');
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'audio/*';
            input.multiple = true; // Allow multiple audio selection
            input.style.display = 'none';

            input.onchange = function(e) {
                const files = e.target.files;
                console.log('Music files selected:', files.length, 'music files');

                if (files.length > 0) {
                    Array.from(files).forEach((file, index) => {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            console.log(`Music ${index + 1} loaded, adding content block`);
                            addContentBlock('music', {
                                src: e.target.result,
                                name: file.name,
                                size: (file.size / 1024 / 1024).toFixed(2) + ' MB'
                            });
                        };
                        reader.readAsDataURL(file);
                    });
                }
            };

            document.body.appendChild(input);
            input.click();
            document.body.removeChild(input);
        }

        function addLink() {
            console.log('Add link button clicked');
            // Create link modal
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 10000; display: flex;
                justify-content: center; align-items: center;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 8px; padding: 2rem; max-width: 500px; width: 90%;">
                    <div style="margin-bottom: 1rem;">
                        <h3 style="margin: 0; color: #004AAD;">Add Link</h3>
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; font-weight: 600; margin-bottom: 0.5rem;">Link Title</label>
                        <input type="text" id="linkTitle" placeholder="Enter link title" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; font-weight: 600; margin-bottom: 0.5rem;">URL</label>
                        <input type="url" id="linkUrl" placeholder="https://example.com" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="display: flex; justify-content: flex-end; gap: 1rem;">
                        <button id="cancelLinkBtn" style="padding: 0.75rem 1.5rem; border: 1px solid #ddd; background: white; color: #666; border-radius: 4px; cursor: pointer;">Cancel</button>
                        <button id="addLinkBtn" style="padding: 0.75rem 1.5rem; border: none; background: #CD208B; color: white; border-radius: 4px; cursor: pointer;">Add Link</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            console.log('Link modal created and added to DOM');

            // Event listeners
            modal.querySelector('#cancelLinkBtn').onclick = () => {
                console.log('Link cancelled');
                document.body.removeChild(modal);
            };
            modal.querySelector('#addLinkBtn').onclick = () => {
                const title = modal.querySelector('#linkTitle').value;
                const url = modal.querySelector('#linkUrl').value;
                console.log('Adding link:', { title, url });
                if (title.trim() && url.trim()) {
                    addContentBlock('link', { title, url });
                    document.body.removeChild(modal);
                } else {
                    showPortfolioNotification('Please enter both title and URL', 'error', 'fas fa-link');
                }
            };
        }

        function addContentBlock(type, data) {
            const container = document.getElementById('portfolioProjectContent');
            const blockId = 'block_' + Date.now();

            let blockHTML = '';

            switch(type) {
                case 'image':
                    blockHTML = `
                        <div class="content-block" id="${blockId}" style="margin-bottom: 1rem; padding: 0; border: none; border-radius: 4px; position: relative;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px; z-index: 10;">×</button>
                            <img src="${data.src}" alt="" style="width: 100%; height: auto; border-radius: 4px; cursor: pointer; display: block;" onclick="openImagePreview('${data.src}', '')">
                        </div>
                    `;
                    break;
                case 'video':
                    blockHTML = `
                        <div class="content-block" id="${blockId}" style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; position: relative;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px;">×</button>
                            <video controls style="max-width: 100%; height: auto; border-radius: 4px;">
                                <source src="${data.src}" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                        </div>
                    `;
                    break;
                case 'text':
                    blockHTML = `
                        <div class="content-block text-block" id="${blockId}" style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; position: relative; display: none !important;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px;">×</button>
                            ${data.heading ? `<h4 style="margin: 0 0 0.5rem 0; color: #004AAD;">${data.heading}</h4>` : ''}
                            <p style="margin: 0; line-height: 1.6; white-space: pre-wrap;">${data.content}</p>
                        </div>
                    `;
                    break;
                case 'pdf':
                    blockHTML = `
                        <div class="content-block pdf-block" id="${blockId}" style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; position: relative; display: none !important; align-items: center; gap: 1rem;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px;">×</button>
                            <i class="fas fa-file-pdf" style="font-size: 2rem; color: #ff4444;"></i>
                            <div>
                                <p style="margin: 0; font-weight: 600;">${data.name}</p>
                                <p style="margin: 0; font-size: 0.9rem; color: #666;">${data.size}</p>
                            </div>
                        </div>
                    `;
                    break;
                case 'music':
                    blockHTML = `
                        <div class="content-block" id="${blockId}" style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; position: relative;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px;">×</button>
                            <audio controls style="width: 100%;">
                                <source src="${data.src}" type="audio/mpeg">
                                Your browser does not support the audio element.
                            </audio>
                        </div>
                    `;
                    break;
                case 'link':
                    blockHTML = `
                        <div class="content-block link-block" id="${blockId}" style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; position: relative; display: none !important; align-items: center; gap: 1rem;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px;">×</button>
                            <i class="fas fa-link" style="font-size: 1.5rem; color: #004AAD;"></i>
                            <div style="flex: 1;">
                                <a href="${data.url}" target="_blank" style="color: #004AAD; text-decoration: none; font-weight: 600; display: block;">${data.title}</a>
                                <p style="margin: 0; font-size: 0.9rem; color: #666; word-break: break-all;">${data.url}</p>
                            </div>
                        </div>
                    `;
                    break;
            }

            container.insertAdjacentHTML('beforeend', blockHTML);
        }

        function removeBlock(blockId) {
            const block = document.getElementById(blockId);
            if (block) {
                block.remove();
            }
        }

        function openImagePreview(src, name) {
            console.log('Opening image preview for:', name);
            // Create image preview modal
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.8); z-index: 10000; display: flex;
                justify-content: center; align-items: center; cursor: pointer;
            `;

            modal.innerHTML = `
                <div style="max-width: 90%; max-height: 90%; position: relative;">
                    <button onclick="this.parentElement.parentElement.remove()" style="position: absolute; top: -40px; right: 0; background: white; color: #333; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; font-size: 16px;">×</button>
                    <img src="${src}" alt="" style="max-width: 100%; max-height: 100%; border-radius: 8px; box-shadow: 0 4px 20px rgba(0,0,0,0.3);">
                </div>
            `;

            // Close modal when clicking outside the image
            modal.onclick = function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            };

            document.body.appendChild(modal);
        }

        // Setup portfolio button event listeners
        function setupPortfolioButtons() {
            console.log('Setting up portfolio button listeners');
            const imageBtn = document.getElementById('uploadImageBtn');
            const videoBtn = document.getElementById('portfolioUploadVideoBtn');
            const textBtn = document.getElementById('addTextBlockBtn');
            const linkBtn = document.getElementById('addLinkBtn');
            const pdfBtn = document.getElementById('uploadPdfBtn');
            const musicBtn = document.getElementById('addMusicBtn');

            console.log('Image button found:', imageBtn);
            console.log('Video button found:', videoBtn);
            console.log('Text button found:', textBtn);
            console.log('Link button found:', linkBtn);
            console.log('PDF button found:', pdfBtn);
            console.log('Music button found:', musicBtn);

            if (imageBtn) {
                imageBtn.removeEventListener('click', addImageUpload); // Remove existing listener
                imageBtn.addEventListener('click', addImageUpload);
            }
            if (videoBtn) {
                videoBtn.removeEventListener('click', addVideoUpload);
                videoBtn.addEventListener('click', addVideoUpload);
            }
            if (textBtn) {
                textBtn.removeEventListener('click', addTextBlock);
                textBtn.addEventListener('click', addTextBlock);
            }
            if (linkBtn) {
                linkBtn.removeEventListener('click', addLink);
                linkBtn.addEventListener('click', addLink);
            }
            if (pdfBtn) {
                pdfBtn.removeEventListener('click', addPdfUpload);
                pdfBtn.addEventListener('click', addPdfUpload);
            }
            if (musicBtn) {
                musicBtn.removeEventListener('click', addMusic);
                musicBtn.addEventListener('click', addMusic);
            }
        }

        });

        // Header functionality
        document.addEventListener('DOMContentLoaded', function() {

            // Profile dropdown functionality
            const profileButton = document.querySelector('.profile-button');
            const profileDropdown = document.querySelector('.profile-dropdown');
            const profileDropdownContent = document.querySelector('.profile-dropdown-content');

            if (profileButton && profileDropdown && profileDropdownContent) {
                profileButton.addEventListener('click', function(e) {
                    e.stopPropagation();
                    profileDropdownContent.classList.toggle('active');
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!profileDropdown.contains(e.target)) {
                        profileDropdownContent.classList.remove('active');
                    }
                });

                // Close dropdown when pressing Escape
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape') {
                        profileDropdownContent.classList.remove('active');
                        profileButton.focus();
                    }
                });

                // Keyboard navigation within dropdown
                profileDropdownContent.addEventListener('keydown', function(e) {
                    const links = Array.from(profileDropdownContent.querySelectorAll('a'));
                    const currentIndex = links.indexOf(document.activeElement);

                    if (e.key === 'ArrowDown') {
                        e.preventDefault();
                        const nextIndex = (currentIndex + 1) % links.length;
                        links[nextIndex].focus();
                    } else if (e.key === 'ArrowUp') {
                        e.preventDefault();
                        const prevIndex = (currentIndex - 1 + links.length) % links.length;
                        links[prevIndex].focus();
                    } else if (e.key === 'Escape') {
                        e.preventDefault();
                        profileDropdownContent.classList.remove('active');
                        profileButton.focus();
                    }
                });
            }

            // Notification functionality
            const notificationBell = document.getElementById('notification-bell');
            const notificationDropdown = document.querySelector('.notification-dropdown');

            if (notificationBell && notificationDropdown) {
                notificationBell.addEventListener('click', function(e) {
                    e.stopPropagation();
                    notificationDropdown.classList.toggle('active');
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!notificationDropdown.contains(e.target) && !notificationBell.contains(e.target)) {
                        notificationDropdown.classList.remove('active');
                    }
                });
            }

            // Search functionality
            function performSearch() {
                const searchInput = document.getElementById('searchInput');
                if (searchInput && searchInput.value.trim()) {
                    console.log('Searching for:', searchInput.value);
                    // Add search functionality here
                }
            }

            // Make performSearch available globally
            window.performSearch = performSearch;



            // Enhanced Open Add Portfolio Modal - Same Style as Edit Profile
            window.openAddPortfolioModal = function() {
                console.log('🔄 Opening portfolio modal...');
                const modal = document.getElementById('addPortfolioModal');

                if (modal) {
                    // Reset form
                    const form = document.getElementById('portfolioForm');
                    if (form) {
                        form.reset();
                    }

                    // Show modal with same method as edit profile
                    modal.style.display = 'block';
                    modal.style.visibility = 'visible';
                    modal.style.opacity = '1';
                    modal.style.zIndex = '99999';
                    modal.classList.add('active');

                    // Prevent body scrolling
                    document.body.style.overflow = 'hidden';

                    console.log('✅ Portfolio Modal opened successfully');
                } else {
                    console.error('❌ Portfolio Modal not found');
                }
            };

            // Enhanced Close Add Portfolio Modal - Same Style as Edit Profile
            window.closeAddPortfolioModal = function() {
                const modal = document.getElementById('addPortfolioModal');
                if (modal) {
                    modal.classList.remove('active');
                    modal.style.display = 'none';
                    modal.style.visibility = 'hidden';
                    modal.style.opacity = '0';
                    document.body.style.overflow = 'auto'; // Restore scrolling

                    // Clear the form and skills
                    const form = document.getElementById('portfolioForm');
                    if (form) {
                        form.reset();
                    }
                    clearProjectSkills();

                    console.log('✅ Portfolio Modal closed');
                }
            };

            // Close modal when clicking outside
            window.addEventListener('click', function(event) {
                const modal = document.getElementById('addPortfolioModal');
                if (event.target === modal) {
                    closeAddPortfolioModal();
                }
            });

            // Close modal with Escape key
            window.addEventListener('keydown', function(event) {
                if (event.key === 'Escape') {
                    const modal = document.getElementById('addPortfolioModal');
                    if (modal && modal.style.display === 'block') {
                        closeAddPortfolioModal();
                    }
                }
            });

            // Function moved outside DOMContentLoaded block - see below

            // Delete Portfolio (Updated for new system)
            window.deletePortfolio = async function(portfolioId) {
                console.log('🗑️ Delete portfolio clicked for ID:', portfolioId);

                if (!confirm('Are you sure you want to delete this portfolio project? This action cannot be undone.')) {
                    return;
                }

                try {
                    const response = await fetch(`/api/portfolios/${portfolioId}`, {
                        method: 'DELETE',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });

                    const data = await response.json();

                    if (data.success) {
                        showPortfolioNotification('Portfolio deleted successfully!', 'success', 'fas fa-trash');
                        // Reload page to update portfolio list
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        showPortfolioNotification('Error: ' + data.error, 'error', 'fas fa-exclamation-triangle');
                    }
                } catch (error) {
                    showPortfolioNotification('An error occurred while deleting', 'error', 'fas fa-exclamation-circle');
                    console.error('Error deleting portfolio:', error);
                }
            };

            // Edit Portfolio
            window.editPortfolio = function(portfolioId) {
                console.log('✏️ Edit portfolio clicked for ID:', portfolioId);
                // TODO: Implement edit functionality
                showPortfolioNotification('Edit functionality coming soon!', 'info', 'fas fa-info-circle');
            };

            // Toast notification function is now defined at the top of the file

        });

        // Function moved to separate script block below

        // Test if elements exist when page loads
        console.log('🔍 Testing dropdown elements...');
        const notificationBell = document.getElementById('notification-bell');
        const notificationDropdown = document.getElementById('notification-dropdown');
        const profileButton = document.querySelector('.profile-button');
        const profileDropdown = document.getElementById('profile-dropdown-content');

        console.log('Elements found:', {
            notificationBell: !!notificationBell,
            notificationDropdown: !!notificationDropdown,
            profileButton: !!profileButton,
            profileDropdown: !!profileDropdown
        });

        // Test function to manually show profile dropdown
        window.testProfileDropdown = function() {
            console.log('🧪 Testing profile dropdown manually...');
            const dropdown = document.getElementById('profile-dropdown-content');
            if (dropdown) {
                dropdown.classList.add('active');
                console.log('✅ Added active class to profile dropdown');
                console.log('Classes after adding active:', dropdown.className);
                console.log('Display style:', window.getComputedStyle(dropdown).display);
            } else {
                console.error('❌ Profile dropdown not found for test');
            }
        };

        // VERY SIMPLE dropdown functions
        function toggleNotificationDropdown() {
            console.log('🔔 Notification clicked!');
            const dropdown = document.getElementById('notification-dropdown');
            const profileDropdown = document.getElementById('profile-dropdown-content');

            // Close profile dropdown
            if (profileDropdown) {
                profileDropdown.style.display = 'none';
            }

            // Toggle notification dropdown
            if (dropdown) {
                if (dropdown.style.display === 'block') {
                    dropdown.style.display = 'none';
                } else {
                    dropdown.style.display = 'block';
                }
                console.log('Notification dropdown display:', dropdown.style.display);
            } else {
                console.error('Notification dropdown not found!');
            }
        }

        function toggleProfileDropdown() {
            console.log('👤 Profile clicked!');
            const dropdown = document.getElementById('profile-dropdown-content');
            const notificationDropdown = document.getElementById('notification-dropdown');

            // Close notification dropdown
            if (notificationDropdown) {
                notificationDropdown.style.display = 'none';
            }

            // Toggle profile dropdown
            if (dropdown) {
                if (dropdown.style.display === 'block') {
                    dropdown.style.display = 'none';
                } else {
                    dropdown.style.display = 'block';
                }
                console.log('Profile dropdown display:', dropdown.style.display);
            } else {
                console.error('Profile dropdown not found!');
            }
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(e) {
            const notificationBell = document.getElementById('notification-bell');
            const profileButton = document.querySelector('.profile-button');
            const notificationDropdown = document.getElementById('notification-dropdown');
            const profileDropdown = document.getElementById('profile-dropdown-content');

            // If click is not on notification bell or dropdown, close it
            if (notificationDropdown && !notificationBell.contains(e.target) && !notificationDropdown.contains(e.target)) {
                notificationDropdown.style.display = 'none';
            }

            // If click is not on profile button or dropdown, close it
            if (profileDropdown && !profileButton.contains(e.target) && !profileDropdown.contains(e.target)) {
                profileDropdown.style.display = 'none';
            }
        });

    </script>

    <!-- Separate Script for Portfolio Save Function -->
    <script>
        // Define savePortfolio function in global scope
        function savePortfolio() {
            console.log('🔄 Save Portfolio function called!');

            // Get form data
            const form = document.getElementById('portfolioForm');
            if (!form) {
                showPortfolioNotification('❌ Form not found! Please try again.', 'error', 'fas fa-exclamation-triangle');
                return;
            }

            const formData = new FormData(form);
            const projectTitle = formData.get('project_title')?.trim();
            const projectUrl = formData.get('project_url')?.trim();
            const projectDescription = formData.get('project_description')?.trim();
            const yourRole = formData.get('your_role')?.trim();
            const skills = formData.get('skills')?.trim();
            const status = 'published'; // Default to published for now

            console.log('Form data:', { projectTitle, projectUrl, projectDescription, yourRole, skills, status });

            // Basic validation
            if (!projectTitle) {
                showPortfolioNotification('📝 Project title is required!', 'error', 'fas fa-heading');
                return;
            }

            if (!projectUrl) {
                showPortfolioNotification('🔗 Project URL is required!', 'error', 'fas fa-link');
                return;
            }

            // Prepare data for API
            const portfolioData = {
                project_title: projectTitle,
                project_url: projectUrl,
                project_description: projectDescription,
                your_role: yourRole,
                skills: skills,
                status: status
            };

            console.log('Sending data to API:', portfolioData);

            // Show loading animation on button
            const saveButton = document.querySelector('button[onclick="savePortfolio()"]');
            const originalText = saveButton.innerHTML;
            saveButton.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-right: 0.5rem;"></i>Saving...';
            saveButton.disabled = true;
            saveButton.style.opacity = '0.8';
            saveButton.style.transform = 'scale(0.98)';
            saveButton.style.transition = 'all 0.2s ease';

            // Send to API
            fetch('/api/portfolios', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(portfolioData)
            })
            .then(response => response.json())
            .then(data => {
                console.log('API response:', data);
                if (data.success) {
                    // Update button to show success state
                    saveButton.innerHTML = '<i class="fas fa-check" style="margin-right: 0.5rem;"></i>Saved!';
                    saveButton.style.background = '#28a745';
                    saveButton.style.transform = 'scale(1)';

                    // Show success toast notification
                    showPortfolioNotification('✅ Portfolio saved successfully! 🎉', 'success', 'fas fa-check-circle');

                    // Store timeout ID so it can be cleared if user closes modal manually
                    if (!window.portfolioTimeouts) window.portfolioTimeouts = [];
                    const timeoutId = setTimeout(() => {
                        closePortfolioModal();
                        // Refresh portfolio section to show new portfolio
                        refreshPortfolioSection();
                    }, 2000);
                    window.portfolioTimeouts.push(timeoutId);
                } else {
                    // Restore button on error
                    saveButton.innerHTML = originalText;
                    saveButton.disabled = false;
                    saveButton.style.opacity = '1';
                    saveButton.style.transform = 'scale(1)';
                    saveButton.style.background = '#004AAD';

                    // Show error toast notification
                    showPortfolioNotification('❌ Error: ' + (data.error || 'Failed to save portfolio'), 'error', 'fas fa-exclamation-triangle');
                }
            })
            .catch(error => {
                console.error('Error:', error);

                // Restore button on network error
                saveButton.innerHTML = originalText;
                saveButton.disabled = false;
                saveButton.style.opacity = '1';
                saveButton.style.transform = 'scale(1)';
                saveButton.style.background = '#004AAD';

                // Show network error toast notification
                showPortfolioNotification('🌐 Network error: ' + error.message, 'error', 'fas fa-wifi');
            });
        }

        console.log('✅ savePortfolio function defined globally!', typeof savePortfolio);

        // Backup event listener for portfolio add button - added after everything is loaded
        setTimeout(function() {
            const portfolioAddBtn = document.getElementById('portfolioAddBtn');
            console.log('🔄 Backup check for portfolioAddBtn:', portfolioAddBtn);

            if (portfolioAddBtn && !portfolioAddBtn.hasAttribute('data-listener-added')) {
                console.log('🔧 Adding backup event listener for portfolio add button');
                portfolioAddBtn.setAttribute('data-listener-added', 'true');

                portfolioAddBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('🚀 BACKUP: Portfolio Add button clicked!');

                    const modal = document.getElementById('addPortfolioModal');
                    if (modal) {
                        console.log('📋 BACKUP: Opening modal...');
                        modal.style.display = 'block';
                        modal.style.visibility = 'visible';
                        modal.style.opacity = '1';
                        modal.style.zIndex = '99999';
                        modal.classList.add('active');
                        document.body.style.overflow = 'hidden';

                        // Reset form
                        const form = document.getElementById('portfolioForm');
                        if (form) {
                            form.reset();
                        }

                        console.log('✅ BACKUP: Modal opened successfully');
                    } else {
                        console.error('❌ BACKUP: Modal not found!');
                    }
                });
            }
        }, 1000); // Wait 1 second to ensure everything is loaded

        // Function to close portfolio modal immediately
        function closePortfolioModal() {
            console.log('🔄 Closing portfolio modal immediately...');

            // Clear any pending timeouts
            const timeouts = window.portfolioTimeouts || [];
            timeouts.forEach(timeout => clearTimeout(timeout));
            window.portfolioTimeouts = [];

            // Reset save button to original state
            const saveButton = document.querySelector('button[onclick="savePortfolio()"]');
            if (saveButton) {
                saveButton.innerHTML = 'Save Project';
                saveButton.disabled = false;
                saveButton.style.opacity = '1';
                saveButton.style.transform = 'scale(1)';
                saveButton.style.background = '#004AAD';
            }

            // Close modal
            const modal = document.getElementById('addPortfolioModal');
            if (modal) {
                modal.classList.remove('active');
                modal.style.display = 'none';
                modal.style.visibility = 'hidden';
                modal.style.opacity = '0';
                document.body.style.overflow = 'auto';

                // Clear form
                const form = document.getElementById('portfolioForm');
                if (form) {
                    form.reset();
                }

                // Clear skills if function exists
                if (typeof clearProjectSkills === 'function') {
                    clearProjectSkills();
                }
            }

            console.log('✅ Portfolio modal closed successfully');
        }

        // Function to refresh portfolio section without page reload
        function refreshPortfolioSection() {
            console.log('🔄 Refreshing portfolio section...');

            // Fetch updated portfolios from server
            fetch('/api/portfolios')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.portfolios) {
                        console.log('📊 Updated portfolios:', data.portfolios);

                        // Update portfolio counts and sections
                        updatePortfolioDisplay(data.portfolios);

                        // Reinitialize navigation with new data
                        setTimeout(() => {
                            initializePortfolioNavigation();
                        }, 200);

                        // Show subtle notification that portfolio was added
                        showPortfolioNotification('📁 Portfolio section updated!', 'info', 'fas fa-sync-alt');
                    }
                })
                .catch(error => {
                    console.error('Error refreshing portfolios:', error);
                });
        }

        // Function to update portfolio display with new data
        function updatePortfolioDisplay(portfolios) {
            const publishedProjects = portfolios.filter(p => p.status === 'published');
            const draftProjects = portfolios.filter(p => p.status === 'draft');

            // Update portfolio counts in tabs
            const publishedTab = document.querySelector('.portfolio-tab[data-tab="published"]');
            const draftsTab = document.querySelector('.portfolio-tab[data-tab="drafts"]');

            if (publishedTab) {
                publishedTab.innerHTML = `Published (${publishedProjects.length})`;
            }

            if (draftsTab) {
                draftsTab.innerHTML = `Drafts (${draftProjects.length})`;
            }

            console.log(`✅ Updated portfolio counts: ${publishedProjects.length} published, ${draftProjects.length} drafts`);
        }

        // Portfolio Navigation Function
        let currentPortfolioIndex = 0;
        let portfolioItems = [];

        function initializePortfolioNavigation() {
            console.log('🔄 Initializing Portfolio Navigation...');

            // Get all portfolio cards from the active tab
            const activeTab = document.querySelector('.portfolio-tab.active');
            const activeTabName = activeTab ? activeTab.getAttribute('data-tab') : 'published';

            if (activeTabName === 'published') {
                portfolioItems = document.querySelectorAll('#publishedContent .portfolio-card');
            } else {
                portfolioItems = document.querySelectorAll('#draftsContent .portfolio-card');
            }

            console.log(`📊 Found ${portfolioItems.length} portfolio items in ${activeTabName} tab`);

            // Reset to first item
            currentPortfolioIndex = 0;

            // Remove active class from all items first
            portfolioItems.forEach((item, index) => {
                item.classList.remove('active');
                console.log(`❌ Removing active class from portfolio item ${index}`);
            });

            // Add active class to only the first item
            if (portfolioItems.length > 0) {
                portfolioItems[0].classList.add('active');
                console.log(`✅ Adding active class to first portfolio item (index 0)`);
            }

            // Update counter and button states
            updatePortfolioNavigation();
        }

        function navigatePortfolio(direction) {
            if (portfolioItems.length === 0) return;

            if (direction === 'next') {
                currentPortfolioIndex = (currentPortfolioIndex + 1) % portfolioItems.length;
            } else if (direction === 'prev') {
                currentPortfolioIndex = (currentPortfolioIndex - 1 + portfolioItems.length) % portfolioItems.length;
            }

            showPortfolioItem(currentPortfolioIndex);
            updatePortfolioNavigation();
        }

        function showPortfolioItem(index) {
            console.log(`🎯 Showing portfolio item at index: ${index} out of ${portfolioItems.length} total items`);

            // Remove active class from all portfolio items and add to the selected one
            portfolioItems.forEach((item, i) => {
                if (i === index) {
                    item.classList.add('active');
                    console.log(`✅ Adding active class to item ${i}`);
                } else {
                    item.classList.remove('active');
                    console.log(`❌ Removing active class from item ${i}`);
                }
            });
        }

        function updatePortfolioNavigation() {
            const counter = document.getElementById('portfolioCounter');
            const prevBtn = document.getElementById('portfolioPrevBtn');
            const nextBtn = document.getElementById('portfolioNextBtn');
            const navigation = document.querySelector('.portfolio-navigation');

            if (portfolioItems.length === 0) {
                // Hide navigation if no items
                if (navigation) navigation.style.display = 'none';
                return;
            } else {
                // Show navigation if items exist
                if (navigation) navigation.style.display = 'flex';
            }

            // Update counter
            if (counter) {
                counter.textContent = `${currentPortfolioIndex + 1} / ${portfolioItems.length}`;
            }

            // Update button states
            if (portfolioItems.length <= 1) {
                // Disable buttons if only one or no items
                if (prevBtn) {
                    prevBtn.style.opacity = '0.5';
                    prevBtn.style.cursor = 'not-allowed';
                }
                if (nextBtn) {
                    nextBtn.style.opacity = '0.5';
                    nextBtn.style.cursor = 'not-allowed';
                }
            } else {
                // Enable buttons if multiple items
                if (prevBtn) {
                    prevBtn.style.opacity = '1';
                    prevBtn.style.cursor = 'pointer';
                }
                if (nextBtn) {
                    nextBtn.style.opacity = '1';
                    nextBtn.style.cursor = 'pointer';
                }
            }
        }

        // Initialize navigation when page loads and when tabs change
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                initializePortfolioSystem();
                // Ensure portfolio navigation is properly initialized after portfolios load
                setTimeout(() => {
                    initializePortfolioNavigation();
                }, 1000);
            }, 500);
        });

        // Re-initialize when switching tabs
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('portfolio-tab')) {
                setTimeout(() => {
                    currentPortfolioIndex = 0;
                    initializePortfolioNavigation();
                }, 100);
            }
        });

        // Additional initialization on window load as backup
        window.addEventListener('load', function() {
            console.log('🔄 Window loaded - Backup portfolio navigation initialization...');
            setTimeout(() => {
                initializePortfolioNavigation();
            }, 1500);
        });

        // Function to fetch portfolio count from server
        async function fetchPortfolioCount() {
            try {
                const response = await fetch('/api/portfolios');
                const data = await response.json();

                if (data.success && data.portfolios) {
                    const publishedCount = data.portfolios.filter(p => p.status === 'published').length;
                    const draftsCount = data.portfolios.filter(p => p.status === 'draft').length;

                    console.log(`📊 Server portfolio counts - Published: ${publishedCount}, Drafts: ${draftsCount}`);

                    // Update tab labels with counts
                    const publishedTab = document.querySelector('.portfolio-tab[data-tab="published"]');
                    const draftsTab = document.querySelector('.portfolio-tab[data-tab="drafts"]');

                    if (publishedTab) {
                        publishedTab.innerHTML = `Published (${publishedCount})`;
                    }

                    if (draftsTab) {
                        draftsTab.innerHTML = `Drafts (${draftsCount})`;
                    }

                    // Re-initialize navigation with updated counts
                    setTimeout(() => {
                        initializePortfolioNavigation();
                    }, 100);

                    return { published: publishedCount, drafts: draftsCount };
                }
            } catch (error) {
                console.error('Error fetching portfolio count:', error);
                return { published: 0, drafts: 0 };
            }
        }

        // Enhanced initialization that fetches real portfolio counts
        async function initializePortfolioSystem() {
            console.log('🔄 Initializing portfolio system...');

            // Fetch real portfolio counts from server
            const counts = await fetchPortfolioCount();

            // Initialize navigation after getting real counts
            setTimeout(() => {
                initializePortfolioNavigation();
            }, 200);

            console.log('✅ Portfolio system initialized with counts:', counts);
        }

        // Make navigation function globally available
        window.navigatePortfolio = navigatePortfolio;
        window.fetchPortfolioCount = fetchPortfolioCount;
        window.initializePortfolioSystem = initializePortfolioSystem;
    </script>

    <!-- Portfolio Modal - Same Style as Edit Profile -->
    <div class="modal" id="addPortfolioModal" style="display: none; position: fixed; z-index: 10000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.4); visibility: hidden; opacity: 0; transition: all 0.3s ease;">
        <div class="modal-content" style="max-width: 1200px; width: 95%; border-radius: 28px; box-shadow: 0 40px 120px rgba(0,0,0,0.2), 0 20px 60px rgba(0,0,0,0.1); backdrop-filter: blur(20px); animation: modalSlideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1); border: 1px solid rgba(255,255,255,0.3); margin: 2% auto; padding: 0; background: #ffffff;">
            <!-- Simple Clean Header - Same as Edit Profile -->
            <header class="modal-header" style="background: linear-gradient(135deg, #004AAD 0%, #CD208B 100%); color: white; padding: 2rem; border-radius: 28px 28px 0 0; position: relative;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <!-- Logo & Title -->
                    <div style="display: flex; align-items: center; gap: 1rem;">
                        <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo" style="width: 45px; height: 45px; border-radius: 50%; object-fit: cover; object-position: center; background: transparent; transform: scale(1.2);">
                        <div>
                            <h2 style="margin: 0; font-size: 1.8rem; font-weight: 700; font-family: 'Poppins', sans-serif;">Add Portfolio</h2>
                            <p style="margin: 0; opacity: 0.9; font-size: 0.9rem;">Showcase your amazing work</p>
                        </div>
                    </div>

                    <!-- Simple Close Button -->
                    <button onclick="closePortfolioModal()" style="background: rgba(255,255,255,0.2); border: none; color: white; font-size: 1.2rem; cursor: pointer; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </header>

            <!-- Modal Body - Same Style as Edit Profile -->
            <div class="modal-body" style="padding: 2.5rem; background: #ffffff; max-height: 70vh; overflow-y: auto;">
                <form id="portfolioForm">

                    <!-- Two Column Layout - Same as Edit Profile -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                        <!-- Project Title -->
                        <div>
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #333; font-family: 'Poppins', sans-serif;">PROJECT TITLE</label>
                            <input type="text" id="projectTitle" name="project_title" required style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; font-size: 1rem; font-family: 'Poppins', sans-serif; background: #f8f9fa;" placeholder="Enter project title" onfocus="this.style.borderColor='#004AAD'; this.style.background='#ffffff'" onblur="this.style.borderColor='#ddd'; this.style.background='#f8f9fa'">
                        </div>

                        <!-- Project URL -->
                        <div>
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #333; font-family: 'Poppins', sans-serif;">PROJECT URL</label>
                            <input type="url" id="projectUrl" name="project_url" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; font-size: 1rem; font-family: 'Poppins', sans-serif; background: #f8f9fa;" placeholder="https://your-project.com" onfocus="this.style.borderColor='#004AAD'; this.style.background='#ffffff'" onblur="this.style.borderColor='#ddd'; this.style.background='#f8f9fa'">
                        </div>
                    </div>

                    <!-- Facebook-style Link Preview -->
                    <div id="urlPreviewContainer" style="display: none; margin-bottom: 2rem; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden; background: white; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                        <!-- Preview Loading State -->
                        <div id="urlPreviewLoading" style="display: none; padding: 2rem; text-align: center; color: #666;">
                            <i class="fas fa-spinner fa-spin" style="margin-bottom: 0.5rem; font-size: 1.2rem;"></i>
                            <div style="font-size: 0.9rem;">Fetching link preview...</div>
                        </div>

                        <!-- Preview Content -->
                        <div id="urlPreviewContent" style="display: none;">
                            <!-- Preview Image -->
                            <div id="urlPreviewImageContainer" style="display: none; position: relative;">
                                <img id="urlPreviewImage" style="width: 100%; height: 200px; object-fit: cover; background: #f8f9fa;" alt="Preview">
                            </div>

                            <!-- Preview Text Content -->
                            <div style="padding: 1.25rem;">
                                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 0.75rem;">
                                    <h4 id="urlPreviewTitle" style="margin: 0; font-size: 1.1rem; font-weight: 600; color: #333; line-height: 1.3; flex: 1; font-family: 'Poppins', sans-serif;"></h4>
                                    <button type="button" id="removePreviewBtn" style="background: #f1f3f4; border: none; border-radius: 50%; width: 32px; height: 32px; cursor: pointer; margin-left: 1rem; display: flex; align-items: center; justify-content: center; color: #666; font-size: 0.9rem; transition: all 0.2s;" onmouseover="this.style.background='#e8eaed'; this.style.transform='scale(1.05)'" onmouseout="this.style.background='#f1f3f4'; this.style.transform='scale(1)'">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <p id="urlPreviewDescription" style="margin: 0 0 1rem 0; color: #666; font-size: 0.95rem; line-height: 1.5; font-family: 'Poppins', sans-serif;"></p>
                                <div style="display: flex; align-items: center; gap: 0.5rem; font-size: 0.85rem; color: #888; font-family: 'Poppins', sans-serif;">
                                    <i class="fas fa-external-link-alt"></i>
                                    <span id="urlPreviewDomain" style="font-weight: 500;"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Preview Error State -->
                        <div id="urlPreviewError" style="display: none; padding: 2rem; text-align: center; color: #666;">
                            <i class="fas fa-exclamation-triangle" style="margin-bottom: 0.75rem; color: #f39c12; font-size: 1.5rem;"></i>
                            <div style="font-size: 1rem; margin-bottom: 0.5rem; font-weight: 500;">Unable to load preview</div>
                            <div style="font-size: 0.85rem; color: #888;">The link will still work when saved</div>
                        </div>
                    </div>

                    <!-- Full Width Project Description -->
                    <div style="margin-bottom: 2rem;">
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #333; font-family: 'Poppins', sans-serif;">PROJECT DESCRIPTION</label>
                        <textarea id="projectDescription" name="project_description" rows="4" required style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; font-size: 1rem; font-family: 'Poppins', sans-serif; resize: vertical; background: #f8f9fa; line-height: 1.5;" placeholder="Describe your project, the challenges you solved, and the results achieved..." onfocus="this.style.borderColor='#004AAD'; this.style.background='#ffffff'" onblur="this.style.borderColor='#ddd'; this.style.background='#f8f9fa'"></textarea>
                    </div>

                    <!-- Two Column Layout for Role and Technologies -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                        <!-- Your Role -->
                        <div>
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #333; font-family: 'Poppins', sans-serif;">YOUR ROLE (OPTIONAL)</label>
                            <input type="text" id="projectRole" name="your_role" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; font-size: 1rem; font-family: 'Poppins', sans-serif; background: #f8f9fa;" placeholder="e.g. Full Stack Developer, UI/UX Designer, Project Manager" onfocus="this.style.borderColor='#004AAD'; this.style.background='#ffffff'" onblur="this.style.borderColor='#ddd'; this.style.background='#f8f9fa'">
                        </div>

                        <!-- Skills -->
                        <div>
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #333; font-family: 'Poppins', sans-serif;">SKILLS</label>
                            <div class="skills-input-container" style="position: relative;">
                                <input type="text" id="projectSkills" placeholder="Search and select skills..." style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; font-size: 1rem; font-family: 'Poppins', sans-serif; background: #f8f9fa;" onfocus="this.style.borderColor='#004AAD'; this.style.background='#ffffff'" onblur="this.style.borderColor='#ddd'; this.style.background='#f8f9fa'">
                                <div id="skills-dropdown" class="skills-dropdown" style="display: none; position: absolute; top: 100%; left: 0; right: 0; background: white; border: 1px solid #ddd; border-top: none; border-radius: 0 0 4px 4px; max-height: 200px; overflow-y: auto; z-index: 1000; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                                    <!-- Search results will appear here -->
                                </div>
                            </div>
                            <div id="project-skills-list" style="margin-top: 0.5rem; display: flex; flex-wrap: wrap; gap: 0.5rem;">
                                <!-- Selected skills will appear here -->
                            </div>
                            <input type="hidden" id="projectTechnologies" name="skills" value="">
                        </div>
                    </div>



                    <!-- Status Selection - Same Style as Edit Profile -->
                    <div style="margin-bottom: 2rem;">
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #333; font-family: 'Poppins', sans-serif;">STATUS</label>
                        <div style="display: flex; gap: 2rem;">
                            <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                <input type="radio" name="status" value="published" checked style="accent-color: #004AAD;">
                                <span style="font-family: 'Poppins', sans-serif; color: #333;">Publish Now</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                <input type="radio" name="status" value="draft" style="accent-color: #004AAD;">
                                <span style="font-family: 'Poppins', sans-serif; color: #333;">Save as Draft</span>
                            </label>
                        </div>
                    </div>

                    <!-- Action Buttons - Same Style as Edit Profile -->
                    <div style="display: flex; gap: 1rem; justify-content: flex-end; padding-top: 2rem; border-top: 1px solid #e9ecef;">
                        <button type="button" onclick="clearProjectSkills(); document.getElementById('addPortfolioModal').classList.remove('active'); document.getElementById('addPortfolioModal').style.display='none'; document.getElementById('addPortfolioModal').style.visibility='hidden'; document.getElementById('addPortfolioModal').style.opacity='0'; document.body.style.overflow='auto';" style="background: #f8f9fa; color: #6c757d; border: 1px solid #ddd; padding: 0.75rem 1.5rem; border-radius: 4px; cursor: pointer; font-size: 1rem; font-weight: 500; font-family: 'Poppins', sans-serif;">
                            Cancel
                        </button>
                        <button type="button" onclick="savePortfolio()" style="background: #004AAD; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 4px; cursor: pointer; font-size: 1rem; font-weight: 500; font-family: 'Poppins', sans-serif; transition: all 0.3s ease; transform: scale(1);" onmouseover="this.style.background='#003a8c'; this.style.transform='scale(1.02)'" onmouseout="this.style.background='#004AAD'; this.style.transform='scale(1)'" onmousedown="this.style.transform='scale(0.98)'" onmouseup="this.style.transform='scale(1.02)'">
                            Save Project
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="delete-modal">
        <div class="delete-modal-content">
            <div class="delete-modal-header">
                <h3>
                    <i class="fas fa-exclamation-triangle"></i>
                    Delete Video
                </h3>
            </div>
            <div class="delete-modal-body">
                <p>Are you sure you want to delete your profile video? This action cannot be undone.</p>
            </div>
            <div class="delete-modal-actions">
                <button class="delete-modal-btn cancel" onclick="closeDeleteModal()">Cancel</button>
                <button class="delete-modal-btn confirm" onclick="confirmDeleteVideo()">Delete</button>
            </div>
        </div>
    </div>

    <!-- Side toast notifications will be created dynamically -->

    <!-- Simplified Modal Styles -->
    <style>
        .modal input:focus, .modal textarea:focus, .modal select:focus {
            outline: none;
            border-color: #004AAD;
        }

        .modal input[type="radio"]:checked + div {
            color: #004AAD;
        }

        .project-skill-tag {
            background: #004AAD;
            color: white;
            padding: 0.4rem 0.8rem;
            border-radius: 16px;
            font-size: 0.85rem;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s;
            font-family: 'Poppins', sans-serif;
            font-weight: 500;
        }

        .project-skill-tag:hover {
            background: #CD208B;
            transform: translateY(-1px);
        }

        .project-skill-tag .remove-skill {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            cursor: pointer;
        }

        .skills-dropdown {
            font-family: 'Poppins', sans-serif;
        }

        .skills-dropdown-item {
            padding: 0.75rem 1rem;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .skills-dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .skills-dropdown-item.selected {
            background-color: #004AAD;
            color: white;
        }

        .skills-dropdown-item:last-child {
            border-bottom: none;
        }

        .skill-icon {
            width: 16px;
            height: 16px;
            background: #e3f2fd;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            color: #1976d2;
        }

        .skills-dropdown-item.selected .skill-icon {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .no-results {
            padding: 1rem;
            text-align: center;
            color: #666;
            font-style: italic;
        }
    </style>

    <script>
        // Comprehensive Skills Database
        const skillsDatabase = [
            // Programming Languages
            'JavaScript', 'Python', 'Java', 'C++', 'C#', 'PHP', 'Ruby', 'Go', 'Rust', 'Swift', 'Kotlin', 'TypeScript',

            // Frontend Technologies
            'React', 'Vue.js', 'Angular', 'HTML', 'CSS', 'SASS', 'SCSS', 'Bootstrap', 'Tailwind CSS', 'jQuery', 'Next.js', 'Nuxt.js',

            // Backend Technologies
            'Node.js', 'Express.js', 'Django', 'Flask', 'Spring Boot', 'Laravel', 'Ruby on Rails', 'ASP.NET', 'FastAPI',

            // Databases
            'MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'SQLite', 'Oracle', 'SQL Server', 'Firebase', 'DynamoDB',

            // Cloud & DevOps
            'AWS', 'Azure', 'Google Cloud', 'Docker', 'Kubernetes', 'Jenkins', 'Git', 'GitHub', 'GitLab', 'CI/CD',

            // Design & UI/UX
            'UI/UX Design', 'Figma', 'Adobe Photoshop', 'Adobe Illustrator', 'Adobe XD', 'Sketch', 'InVision', 'Canva',

            // Data Science & AI
            'Machine Learning', 'Data Analysis', 'Data Science', 'Artificial Intelligence', 'TensorFlow', 'PyTorch', 'Pandas', 'NumPy',

            // Mobile Development
            'React Native', 'Flutter', 'iOS Development', 'Android Development', 'Xamarin', 'Ionic',

            // CMS & E-commerce
            'WordPress', 'Shopify', 'WooCommerce', 'Drupal', 'Joomla', 'Magento',

            // Marketing & SEO
            'Digital Marketing', 'SEO', 'SEM', 'Google Ads', 'Facebook Ads', 'Content Marketing', 'Social Media Marketing',

            // Other Skills
            'Project Management', 'Agile', 'Scrum', 'API Development', 'REST API', 'GraphQL', 'Microservices', 'Testing', 'QA'
        ];

        // Project Skills Management
        let projectSkills = [];
        let selectedIndex = -1;

        // Initialize project skills functionality
        document.addEventListener('DOMContentLoaded', function() {
            const skillsInput = document.getElementById('projectSkills');
            const dropdown = document.getElementById('skills-dropdown');
            const urlInput = document.getElementById('projectUrl');

            // Initialize URL preview functionality
            if (urlInput) {
                initializeUrlPreview();
            }

            if (skillsInput && dropdown) {
                // Handle input changes for search
                skillsInput.addEventListener('input', function() {
                    const query = this.value.trim();
                    if (query.length > 0) {
                        showSkillSuggestions(query);
                    } else {
                        hideDropdown();
                    }
                    selectedIndex = -1;
                });

                // Handle keyboard navigation
                skillsInput.addEventListener('keydown', function(event) {
                    const dropdown = document.getElementById('skills-dropdown');
                    const items = dropdown.querySelectorAll('.skills-dropdown-item');

                    if (event.key === 'ArrowDown') {
                        event.preventDefault();
                        selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
                        updateSelection(items);
                    } else if (event.key === 'ArrowUp') {
                        event.preventDefault();
                        selectedIndex = Math.max(selectedIndex - 1, -1);
                        updateSelection(items);
                    } else if (event.key === 'Enter') {
                        event.preventDefault();
                        if (selectedIndex >= 0 && items[selectedIndex]) {
                            const skill = items[selectedIndex].textContent.trim();
                            addProjectSkill(skill);
                        } else {
                            const skill = this.value.trim();
                            if (skill) {
                                addProjectSkill(skill);
                            }
                        }
                    } else if (event.key === 'Escape') {
                        hideDropdown();
                        this.blur();
                    }
                });

                // Handle focus and blur
                skillsInput.addEventListener('focus', function() {
                    const query = this.value.trim();
                    if (query.length > 0) {
                        showSkillSuggestions(query);
                    }
                });

                skillsInput.addEventListener('blur', function() {
                    // Delay hiding to allow click on dropdown items
                    setTimeout(() => hideDropdown(), 150);
                });
            }
        });

        async function showSkillSuggestions(query) {
            const dropdown = document.getElementById('skills-dropdown');
            if (!dropdown) return;

            // Show loading state
            dropdown.innerHTML = '<div class="no-results">Searching skills...</div>';
            dropdown.style.display = 'block';

            try {
                // Try to fetch from API first
                const response = await fetch(`/api/skills/search?q=${encodeURIComponent(query)}&limit=8`);
                const data = await response.json();

                let filteredSkills = [];
                if (data.success && data.skills) {
                    // Use API results and filter out already selected skills
                    filteredSkills = data.skills.filter(skill => !projectSkills.includes(skill));
                } else {
                    // Fallback to local database
                    filteredSkills = skillsDatabase.filter(skill =>
                        skill.toLowerCase().includes(query.toLowerCase()) &&
                        !projectSkills.includes(skill)
                    ).slice(0, 8);
                }

                // Clear previous results
                dropdown.innerHTML = '';

                if (filteredSkills.length > 0) {
                    filteredSkills.forEach((skill, index) => {
                        const item = document.createElement('div');
                        item.className = 'skills-dropdown-item';
                        item.innerHTML = `
                            <div class="skill-icon">⚡</div>
                            <span>${skill}</span>
                        `;
                        item.addEventListener('click', () => addProjectSkill(skill));
                        dropdown.appendChild(item);
                    });
                } else {
                    const noResults = document.createElement('div');
                    noResults.className = 'no-results';
                    noResults.textContent = 'No matching skills found. Press Enter to add as custom skill.';
                    dropdown.appendChild(noResults);
                }

                selectedIndex = -1;
            } catch (error) {
                console.error('Error fetching skills:', error);

                // Fallback to local database on error
                const filteredSkills = skillsDatabase.filter(skill =>
                    skill.toLowerCase().includes(query.toLowerCase()) &&
                    !projectSkills.includes(skill)
                ).slice(0, 8);

                dropdown.innerHTML = '';
                if (filteredSkills.length > 0) {
                    filteredSkills.forEach((skill, index) => {
                        const item = document.createElement('div');
                        item.className = 'skills-dropdown-item';
                        item.innerHTML = `
                            <div class="skill-icon">⚡</div>
                            <span>${skill}</span>
                        `;
                        item.addEventListener('click', () => addProjectSkill(skill));
                        dropdown.appendChild(item);
                    });
                } else {
                    const noResults = document.createElement('div');
                    noResults.className = 'no-results';
                    noResults.textContent = 'No matching skills found. Press Enter to add as custom skill.';
                    dropdown.appendChild(noResults);
                }
                selectedIndex = -1;
            }
        }

        function updateSelection(items) {
            items.forEach((item, index) => {
                if (index === selectedIndex) {
                    item.classList.add('selected');
                } else {
                    item.classList.remove('selected');
                }
            });
        }

        function hideDropdown() {
            const dropdown = document.getElementById('skills-dropdown');
            if (dropdown) {
                dropdown.style.display = 'none';
            }
            selectedIndex = -1;
        }

        function addProjectSkill(skill) {
            if (skill && !projectSkills.includes(skill)) {
                projectSkills.push(skill);
                updateProjectSkillsDisplay();
                updateProjectSkillsHiddenInput();

                // Clear input and hide dropdown
                const skillsInput = document.getElementById('projectSkills');
                if (skillsInput) {
                    skillsInput.value = '';
                }
                hideDropdown();
            }
        }

        function removeProjectSkill(skill) {
            const index = projectSkills.indexOf(skill);
            if (index > -1) {
                projectSkills.splice(index, 1);
                updateProjectSkillsDisplay();
                updateProjectSkillsHiddenInput();
            }
        }

        function updateProjectSkillsDisplay() {
            const skillsList = document.getElementById('project-skills-list');
            if (!skillsList) return;

            skillsList.innerHTML = '';
            projectSkills.forEach(skill => {
                const skillTag = document.createElement('span');
                skillTag.className = 'project-skill-tag';
                skillTag.innerHTML = `
                    ${skill}
                    <span class="remove-skill" onclick="removeProjectSkill('${skill}')">&times;</span>
                `;
                skillsList.appendChild(skillTag);
            });
        }

        function updateProjectSkillsHiddenInput() {
            const hiddenInput = document.getElementById('projectTechnologies');
            if (hiddenInput) {
                hiddenInput.value = projectSkills.join(', ');
            }
        }

        // Clear skills when modal is closed
        function clearProjectSkills() {
            projectSkills = [];
            updateProjectSkillsDisplay();
            updateProjectSkillsHiddenInput();
            const skillsInput = document.getElementById('projectSkills');
            if (skillsInput) {
                skillsInput.value = '';
            }
            hideDropdown();
        }

        // Override the existing openAddPortfolioModal function to clear skills
        const originalOpenAddPortfolioModal = window.openAddPortfolioModal;
        window.openAddPortfolioModal = function() {
            if (originalOpenAddPortfolioModal) {
                originalOpenAddPortfolioModal();
            }
            clearProjectSkills();
        };

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const skillsContainer = document.querySelector('.skills-input-container');
            if (skillsContainer && !skillsContainer.contains(event.target)) {
                hideDropdown();
            }
        });

        // Facebook-style URL Preview in Modal
        let urlPreviewTimeout;
        let currentPreviewUrl = '';

        function initializeUrlPreview() {
            const urlInput = document.getElementById('projectUrl');
            const removeBtn = document.getElementById('removePreviewBtn');

            if (urlInput) {
                // Handle URL input changes
                urlInput.addEventListener('input', function() {
                    const url = this.value.trim();

                    // Clear existing timeout
                    clearTimeout(urlPreviewTimeout);

                    if (url && isValidUrl(url)) {
                        // Debounce the preview generation
                        urlPreviewTimeout = setTimeout(() => {
                            if (url !== currentPreviewUrl) {
                                generateUrlPreview(url);
                            }
                        }, 1000); // Wait 1 second after user stops typing
                    } else if (url.length > 3) {
                        // Show a basic preview for invalid URLs too
                        urlPreviewTimeout = setTimeout(() => {
                            showBasicPreview(url);
                        }, 1000);
                    } else {
                        hideUrlPreview();
                    }
                });

                // Handle paste events for immediate preview
                urlInput.addEventListener('paste', function() {
                    setTimeout(() => {
                        const url = this.value.trim();
                        if (url && isValidUrl(url)) {
                            generateUrlPreview(url);
                        }
                    }, 100);
                });
            }

            if (removeBtn) {
                removeBtn.addEventListener('click', function() {
                    hideUrlPreview();
                    currentPreviewUrl = '';
                });
            }
        }

        function isValidUrl(string) {
            try {
                // Add protocol if missing
                const url = string.startsWith('http') ? string : 'https://' + string;
                new URL(url);
                return true;
            } catch (_) {
                return false;
            }
        }

        async function generateUrlPreview(url) {
            const container = document.getElementById('urlPreviewContainer');
            const loading = document.getElementById('urlPreviewLoading');
            const content = document.getElementById('urlPreviewContent');
            const error = document.getElementById('urlPreviewError');

            if (!container) return;

            // Show container and loading state
            container.style.display = 'block';
            loading.style.display = 'block';
            content.style.display = 'none';
            error.style.display = 'none';

            currentPreviewUrl = url;

            try {
                // Add protocol if missing
                const fullUrl = url.startsWith('http') ? url : 'https://' + url;

                const response = await fetch('/api/link-preview', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ url: fullUrl })
                });

                const data = await response.json();

                // Hide loading
                loading.style.display = 'none';

                if (data.success) {
                    displayUrlPreview(data);
                } else {
                    console.error('API Error:', data.error);
                    // Even on API error, show a basic preview
                    displayUrlPreview({
                        title: 'External Project Link',
                        description: 'Click to view this project',
                        image: `https://www.google.com/s2/favicons?domain=${new URL(fullUrl).hostname}&sz=128`,
                        url: fullUrl
                    });
                }
            } catch (err) {
                console.error('Error generating URL preview:', err);
                loading.style.display = 'none';

                // Always show a preview, even on network errors
                try {
                    const domain = new URL(url.startsWith('http') ? url : 'https://' + url).hostname;
                    displayUrlPreview({
                        title: `Project on ${domain}`,
                        description: 'Click to view this project',
                        image: `https://www.google.com/s2/favicons?domain=${domain}&sz=128`,
                        url: url
                    });
                } catch (urlError) {
                    // Ultimate fallback
                    displayUrlPreview({
                        title: 'External Project Link',
                        description: 'Click to view this project',
                        image: 'https://www.google.com/s2/favicons?domain=example.com&sz=128',
                        url: url
                    });
                }
            }
        }

        function displayUrlPreview(data) {
            const content = document.getElementById('urlPreviewContent');
            const title = document.getElementById('urlPreviewTitle');
            const description = document.getElementById('urlPreviewDescription');
            const domain = document.getElementById('urlPreviewDomain');
            const imageContainer = document.getElementById('urlPreviewImageContainer');
            const image = document.getElementById('urlPreviewImage');

            if (!content) return;

            // Set text content
            title.textContent = data.title || 'Project Link';
            description.textContent = data.description || 'Click to view this project';

            try {
                const urlObj = new URL(data.url);
                domain.textContent = urlObj.hostname;
            } catch (e) {
                domain.textContent = 'External Link';
            }

            // Handle image
            if (data.image) {
                image.src = data.image;
                image.onload = function() {
                    imageContainer.style.display = 'block';
                };
                image.onerror = function() {
                    imageContainer.style.display = 'none';
                };
            } else {
                imageContainer.style.display = 'none';
            }

            content.style.display = 'block';
        }

        function showUrlPreviewError(url, errorMessage = null) {
            const error = document.getElementById('urlPreviewError');
            if (error) {
                // Update error message if provided
                if (errorMessage) {
                    const errorDiv = error.querySelector('div:last-child');
                    if (errorDiv) {
                        errorDiv.textContent = `Error: ${errorMessage}`;
                    }
                }
                error.style.display = 'block';
            }
        }

        function showBasicPreview(url) {
            const container = document.getElementById('urlPreviewContainer');
            const loading = document.getElementById('urlPreviewLoading');
            const content = document.getElementById('urlPreviewContent');
            const error = document.getElementById('urlPreviewError');

            if (!container) return;

            // Show container
            container.style.display = 'block';
            loading.style.display = 'none';
            content.style.display = 'none';
            error.style.display = 'none';

            // Try to extract domain for basic preview
            let domain = url;
            let title = 'Project Link';
            let description = 'Click to view this project';

            try {
                // Try to extract domain-like part
                const cleanUrl = url.replace(/^https?:\/\//, '').replace(/^www\./, '');
                const domainPart = cleanUrl.split('/')[0];
                if (domainPart) {
                    domain = domainPart;
                    title = `Project on ${domain}`;
                    description = `View this project on ${domain}`;
                }
            } catch (e) {
                // Use defaults
            }

            // Display basic preview
            displayUrlPreview({
                title: title,
                description: description,
                image: `https://www.google.com/s2/favicons?domain=${domain}&sz=128`,
                url: url
            });
        }

        function hideUrlPreview() {
            const container = document.getElementById('urlPreviewContainer');
            if (container) {
                container.style.display = 'none';
            }
        }

        // Clear URL preview when modal is closed
        const originalCloseAddPortfolioModal = window.closeAddPortfolioModal;
        window.closeAddPortfolioModal = function() {
            if (originalCloseAddPortfolioModal) {
                originalCloseAddPortfolioModal();
            }
            hideUrlPreview();
            currentPreviewUrl = '';
            clearTimeout(urlPreviewTimeout);
        };

        // Portfolio View Modal Functions
        let currentPortfolioData = {};

        window.openPortfolioViewModal = function(id, title, type, description, skills, url, role) {
            console.log('Opening portfolio view modal for:', { id, title, type, description, skills, url, role });

            // Store current portfolio data
            currentPortfolioData = { id, title, type, description, skills, url, role };

            // Populate modal content
            document.getElementById('portfolioModalTitle').textContent = title || 'Untitled Project';
            document.getElementById('portfolioModalSubtitle').textContent = type || 'Project';

            // Description section
            const descSection = document.getElementById('portfolioDescriptionSection');
            const descContent = document.getElementById('portfolioModalDescription');
            if (description && description.trim()) {
                descContent.textContent = description;
                descSection.style.display = 'block';
            } else {
                descSection.style.display = 'none';
            }

            // Role section
            const roleSection = document.getElementById('portfolioRoleSection');
            const roleContent = document.getElementById('portfolioModalRole');
            if (role && role.trim()) {
                roleContent.textContent = role;
                roleSection.style.display = 'block';
            } else {
                roleContent.textContent = 'Not specified';
                roleSection.style.display = 'block';
            }

            // Skills section
            const skillsSection = document.getElementById('portfolioSkillsSection');
            const skillsContainer = document.getElementById('portfolioModalSkills');
            if (skills && skills.trim()) {
                skillsContainer.innerHTML = '';
                const skillArray = skills.split(',');
                skillArray.forEach(skill => {
                    if (skill.trim()) {
                        const skillTag = document.createElement('span');
                        skillTag.className = 'portfolio-modal-skill-tag';
                        skillTag.textContent = skill.trim();
                        skillsContainer.appendChild(skillTag);
                    }
                });
                skillsSection.style.display = 'block';
            } else {
                skillsSection.style.display = 'none';
            }

            // URL section
            const urlSection = document.getElementById('portfolioUrlSection');
            const urlContainer = document.getElementById('portfolioModalUrl');
            const visitBtn = document.getElementById('portfolioVisitBtn');
            if (url && url.trim()) {
                urlContainer.innerHTML = `<a href="${url}" target="_blank" class="portfolio-modal-url">${url}</a>`;
                urlSection.style.display = 'block';
                visitBtn.style.display = 'inline-flex';
                visitBtn.setAttribute('data-url', url);
            } else {
                urlSection.style.display = 'none';
                visitBtn.style.display = 'none';
            }

            // Show modal
            document.getElementById('portfolioViewModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        };

        window.closePortfolioViewModal = function() {
            document.getElementById('portfolioViewModal').style.display = 'none';
            document.body.style.overflow = 'auto';
            currentPortfolioData = {};
        };



        window.visitPortfolioUrl = function() {
            const url = document.getElementById('portfolioVisitBtn').getAttribute('data-url');
            if (url) {
                window.open(url, '_blank');
            }
        };

        // Close modal when clicking outside
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('portfolioViewModal');
            if (event.target === modal) {
                closePortfolioViewModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const modal = document.getElementById('portfolioViewModal');
                if (modal.style.display === 'block') {
                    closePortfolioViewModal();
                }
            }
        });
    </script>

    <!-- Portfolio View Modal -->
    <div id="portfolioViewModal" class="portfolio-view-modal">
        <div class="portfolio-view-modal-content">
            <div class="portfolio-modal-header">
                <button class="portfolio-modal-close" onclick="closePortfolioViewModal()">&times;</button>
                <h2 class="portfolio-modal-title" id="portfolioModalTitle">Project Title</h2>
                <p class="portfolio-modal-subtitle" id="portfolioModalSubtitle">Project Type</p>
            </div>
            <div class="portfolio-modal-body">
                <div class="portfolio-modal-section" id="portfolioDescriptionSection">
                    <div class="portfolio-modal-section-title">Project description</div>
                    <div class="portfolio-modal-description" id="portfolioModalDescription">
                        Project description will appear here...
                    </div>
                </div>

                <div class="portfolio-modal-section" id="portfolioRoleSection">
                    <div class="portfolio-modal-section-title">Your Role</div>
                    <div class="portfolio-modal-description" id="portfolioModalRole">
                        Your role in this project will appear here...
                    </div>
                </div>

                <div class="portfolio-modal-section" id="portfolioSkillsSection">
                    <div class="portfolio-modal-section-title">Skills and Deliverables</div>
                    <div class="portfolio-modal-skills" id="portfolioModalSkills">
                        <!-- Skills will be populated here -->
                    </div>
                </div>

                <div class="portfolio-modal-section" id="portfolioUrlSection">
                    <div class="portfolio-modal-section-title">Project URL</div>
                    <div id="portfolioModalUrl">
                        <!-- URL will be populated here -->
                    </div>
                </div>
            </div>
            <div class="portfolio-modal-actions">
                <button class="portfolio-modal-btn portfolio-modal-btn-secondary" onclick="closePortfolioViewModal()">
                    Close
                </button>
                <button class="portfolio-modal-btn portfolio-modal-btn-primary" id="portfolioVisitBtn" onclick="visitPortfolioUrl()" style="display: none;">
                    <i class="fas fa-external-link-alt"></i> Visit Project
                </button>
            </div>
        </div>
    </div>

</body>
</html>




 